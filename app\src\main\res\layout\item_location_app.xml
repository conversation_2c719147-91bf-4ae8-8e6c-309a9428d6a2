<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/item_app_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            tools:src="@mipmap/ic_launcher"
            />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/item_app_name"
                style="@style/TextAppearance.AppCompat.Body1"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginLeft="20dp"
                android:layout_marginStart="20dp"
                android:layout_weight="1"
                android:gravity="center|start"
                tools:text="App Label"/>

            <TextView
                android:id="@+id/item_location"
                style="@style/TextAppearance.AppCompat.Caption"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginLeft="20dp"
                android:layout_marginStart="20dp"
                android:layout_weight="1"
                android:gravity="center|start"
                tools:text="22,114"/>

        </LinearLayout>
    </LinearLayout>
</RelativeLayout>