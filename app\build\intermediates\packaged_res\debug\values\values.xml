<?xml version="1.0" encoding="utf-8"?>
<resources>
    <integer-array name="progress_colors">
        <item>@color/holo_blue_dark</item>
        <item>@color/holo_yellow_dark</item>
        <item>@color/holo_green_dark</item>
        <item>@color/holo_purple_dark</item>
        <item>@color/holo_red_dark</item>
    </integer-array>
    <color name="black_20_transparent">#55000000</color>
    <color name="colorAccent">#607191</color>
    <color name="colorPrimary">#607191</color>
    <color name="colorPrimaryDark">#2A364C</color>
    <color name="colorPrimaryRavel">#607191</color>
    <color name="desktopColorA">#2C3B4E</color>
    <color name="desktopColorB">#314155</color>
    <color name="desktopColorC">#324257</color>
    <color name="desktopColorD">#2a3646</color>
    <color name="holo_blue_dark">#33cccc</color>
    <color name="holo_green_dark">#67e667</color>
    <color name="holo_purple_dark">#df38b1</color>
    <color name="holo_red_dark">#ff4040</color>
    <color name="holo_yellow_dark">#ff9640</color>
    <color name="mainTextColor">#F0DEB7</color>
    <color name="md_transparent">#00FFFFFF</color>
    <color name="transparent">#00000000</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="card_gap">60dp</dimen>
    <dimen name="card_gap_bottom">5dp</dimen>
    <dimen name="card_radius">5dp</dimen>
    <dimen name="desktop_divider">0.5dp</dimen>
    <dimen name="dp10">10dp</dimen>
    <dimen name="dp16">16dp</dimen>
    <dimen name="dp24">24dp</dimen>
    <dimen name="dp30">30dp</dimen>
    <dimen name="dp8">8dp</dimen>
    <dimen name="dp80">80dp</dimen>
    <dimen name="dsrv_defaultHotspotHeight">56dp</dimen>
    <dimen name="item_height">60dp</dimen>
    <dimen name="line_height">40dp</dimen>
    <item name="cardstack_internal_position_tag" type="id"/>
    <integer name="parallax_scale_default">-2</integer>
    <string name="about">About</string>
    <string name="add_app">Add App</string>
    <string name="app_name">VirtualApp</string>
    <string name="brand">Brand</string>
    <string name="clone_apps">Clone Apps</string>
    <string name="config_device_info">Device Info</string>
    <string name="create_shortcut">Create shortcut</string>
    <string name="delete">Delete</string>
    <string name="desktop">Desktop</string>
    <string name="device">Device</string>
    <string name="enable">Enable</string>
    <string name="external_storage">External Storage</string>
    <string name="fake_device_info">Fake Device Info</string>
    <string name="install_d">Install (%d)</string>
    <string name="install_too_much_once_time">No more then 9 apps can be chosen at a time!</string>
    <string name="manufacturer">Manufacturer</string>
    <string name="new_user">New User</string>
    <string name="preparing">Opening the app…</string>
    <string name="save">Save</string>
    <string name="save_success">Save success!</string>
    <string name="wait">Please wait…</string>
    <string name="wifi_status">Wifi Status</string>
    <style name="AVLoadingIndicatorView">
        <item name="minWidth">48dip</item>
        <item name="maxWidth">48dip</item>
        <item name="minHeight">48dip</item>
        <item name="maxHeight">48dip</item>
        <item name="indicatorName">BallPulseIndicator</item>
    </style>
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="TransparentTheme" parent="AppTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@android:color/background_dark</item>
        <item name="colorPrimaryDark">@android:color/background_dark</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>
    <style name="UITheme" parent="Theme.AppCompat.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="VAAlertTheme" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <declare-styleable name="CardStackLayout">
        <attr format="boolean" name="parallax_enabled"/>
        <attr format="integer" name="parallax_scale"/>
        <attr format="dimension" name="card_gap"/>
        <attr format="dimension" name="card_gap_bottom"/>
        <attr format="boolean" name="showInitAnimation"/>
    </declare-styleable>
    <declare-styleable name="DragSelectRecyclerView">
        <attr format="dimension" name="dsrv_autoScrollHotspotHeight"/>
        <attr format="boolean" name="dsrv_autoScrollEnabled"/>
        <attr format="dimension" name="dsrv_autoScrollHotspot_offsetTop"/>
        <attr format="dimension" name="dsrv_autoScrollHotspot_offsetBottom"/>
    </declare-styleable>
    <declare-styleable name="FitTextView">
        <attr format="dimension" name="ftMinTextSize"/>
        <attr format="dimension" name="ftMaxTextSize"/>
    </declare-styleable>
    <declare-styleable name="LabelView">
        
        <attr format="string" name="lv_text"/>
        
        <attr format="color" name="lv_text_color"/>
        
        <attr format="dimension" name="lv_text_size"/>
        
        <attr format="boolean" name="lv_text_bold"/>
        
        <attr format="boolean" name="lv_text_all_caps"/>
        
        <attr format="color" name="lv_background_color"/>
        
        <attr format="dimension" name="lv_min_size"/>
        
        <attr format="dimension" name="lv_padding"/>
        
        <attr format="enum" name="lv_gravity">
            <enum name="TOP_LEFT" value="51"/>
            <enum name="TOP_RIGHT" value="53"/>
            <enum name="BOTTOM_LEFT" value="83"/>
            <enum name="BOTTOM_RIGHT" value="85"/>
        </attr>
        
        <attr format="boolean" name="lv_fill_triangle"/>

    </declare-styleable>
    <declare-styleable name="LoadingIndicatorView">
        <attr format="dimension" name="minWidth"/>
        <attr format="dimension" name="maxWidth"/>
        <attr format="dimension" name="minHeight"/>
        <attr format="dimension" name="maxHeight"/>
        <attr format="string" name="indicatorName"/>
        <attr format="color" name="indicatorColor"/>
    </declare-styleable>
    <declare-styleable name="MaterialRippleLayout">
        <attr format="color" localization="suggested" name="mrl_rippleColor"/>
        <attr format="dimension" localization="suggested" name="mrl_rippleDimension"/>
        <attr format="boolean" localization="suggested" name="mrl_rippleOverlay"/>
        <attr format="float" localization="suggested" name="mrl_rippleAlpha"/>
        <attr format="integer" localization="suggested" name="mrl_rippleDuration"/>
        <attr format="integer" localization="suggested" name="mrl_rippleFadeDuration"/>
        <attr format="boolean" localization="suggested" name="mrl_rippleHover"/>
        <attr format="color" localization="suggested" name="mrl_rippleBackground"/>
        <attr format="boolean" localization="suggested" name="mrl_rippleDelayClick"/>
        <attr format="boolean" localization="suggested" name="mrl_ripplePersistent"/>
        <attr format="boolean" localization="suggested" name="mrl_rippleInAdapter"/>
        <attr format="dimension" localization="suggested" name="mrl_rippleRoundedCorners"/>
    </declare-styleable>
    <declare-styleable name="ProgressImageView">
        <attr format="integer" name="pi_progress"/>
        <attr format="color" name="pi_mask_color"/>
        <attr format="dimension" name="pi_stroke"/>
        <attr format="dimension" name="pi_radius"/>
        <attr format="boolean" name="pi_force_square"/>
    </declare-styleable>
    <declare-styleable name="RippleButton">
        <attr format="color" name="rippleColor"/>
        <attr format="float" name="alphaFactor"/>
        <attr format="boolean" name="hover"/>
    </declare-styleable>
    <declare-styleable name="ShimmerView">
        <attr format="color" name="reflectionColor"/>
    </declare-styleable>
</resources>