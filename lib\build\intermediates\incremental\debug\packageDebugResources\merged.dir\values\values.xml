<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="match_parent">-1px</dimen>
    <dimen name="notification_max_height">256dp</dimen>
    <dimen name="notification_mid_height">128dp</dimen>
    <dimen name="notification_min_height">64dp</dimen>
    <dimen name="notification_padding">4dp</dimen>
    <dimen name="notification_panel_width">-1dp</dimen>
    <dimen name="notification_side_padding">8dp</dimen>
    <dimen name="standard_notification_panel_width">416dp
    </dimen>
    <integer name="config_maxResolverActivityColumns">8</integer>
    <string name="add_account_button_label">Add account</string>
    <string name="choose">Choose</string>
    <string name="choose_empty">Chooser is Empty</string>
    <string name="engine_process_name">:x</string>
    <string name="noApplications">No find applications</string>
    <string name="owner_name">Admin</string>
    <string name="virtual_installer">VirtualPackage Installer</string>
    <style name="VAAlertTheme" parent="android:Theme.DeviceDefault.Dialog">
    </style>
    <style name="VATheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowDisablePreview">true</item>
    </style>
    <style name="notification_button">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
    </style>
    <style name="notification_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style>
</resources>