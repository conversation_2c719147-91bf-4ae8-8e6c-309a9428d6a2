1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.virtualapp"
4    android:versionCode="24"
5    android:versionName="1.2.5" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:4:5-66
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
12-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-88
12-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-85
13    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
13-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-77
13-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-74
14    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
14-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-78
14-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-75
15    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
15-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-78
15-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-75
16    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
16-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-80
16-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:22-77
17    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
17-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-81
17-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-78
18    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
18-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:5-95
18-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:22-92
19    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
19-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-117
19-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:22-114
20    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
20-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:5-106
20-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:22-103
21    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
21-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-107
21-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:22-104
22    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
22-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-86
22-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:22-83
23    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
23-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:5-76
23-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:22-73
24    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
24-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-82
24-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:22-79
25    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" />
25-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-83
25-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:22-80
26    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
26-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:5-80
26-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:22-77
27    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
27-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:5-79
27-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:22-76
28    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
28-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-97
28-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:22-94
29    <uses-permission android:name="android.permission.ACCOUNT_MANAGER" />
29-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-27:47
29-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-58
30    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
30-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:5-30:47
30-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-62
31    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
31-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:5-74
31-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:22-71
32    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
32-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:5-81
32-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:22-78
33    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
33-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-79
33-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-76
34    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
34-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-89
34-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-86
35    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
35-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-38:39
35-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-63
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-79
36-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-76
37    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
37-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-76
37-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-73
38    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
38-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-77
38-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-74
39    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
39-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-80
39-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-77
40    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
40-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-45:47
40-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:9-57
41    <uses-permission android:name="android.permission.BLUETOOTH" />
41-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:5-68
41-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:22-65
42    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
42-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:5-74
42-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:22-71
43    <uses-permission android:name="android.permission.BODY_SENSORS" />
43-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:5-71
43-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:22-68
44    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
44-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:5-75
44-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:22-72
45    <uses-permission android:name="android.permission.CALL_PHONE" />
45-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:5-69
45-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:22-66
46    <uses-permission android:name="android.permission.CAMERA" />
46-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:5-65
46-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:22-62
47    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
47-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:5-79
47-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:22-76
48    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
48-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:5-86
48-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:22-83
49    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
49-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:5-76
49-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:22-73
50    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
50-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:5-77
50-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:22-74
51    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
51-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:5-74
51-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:22-71
52    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
52-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:5-75
52-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:22-72
53    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
53-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:5-88
53-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:22-85
54    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
54-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:5-76
54-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:22-73
55    <uses-permission android:name="android.permission.FLASHLIGHT" />
55-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:5-69
55-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:22-66
56    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
56-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:5-71
56-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:22-68
57    <uses-permission android:name="android.permission.GET_CLIPS" />
57-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:5-68
57-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:22-65
58    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
58-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:5-75
58-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:22-72
59    <uses-permission android:name="android.permission.GET_TASKS" />
59-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:5-68
59-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:22-65
60    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
60-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:5-84
60-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:22-81
61    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
61-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:5-74
61-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:22-71
62    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
62-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:5-80
62-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:22-77
63    <uses-permission android:name="android.permission.NFC" />
63-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:5-62
63-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:22-59
64    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
64-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:5-78
64-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:22-75
65    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
65-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:5-81
65-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:22-78
66    <uses-permission android:name="android.permission.READ_CALENDAR" />
66-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:5-72
66-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:22-69
67    <uses-permission android:name="android.permission.READ_CALL_LOG" />
67-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:5-72
67-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:22-69
68    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
68-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:5-79
68-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:22-76
69    <uses-permission android:name="android.permission.READ_CLIPS" />
69-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:5-69
69-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:22-66
70    <uses-permission android:name="android.permission.READ_CONTACTS" />
70-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:5-72
70-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:22-69
71    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
71-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:5-80
71-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:22-77
72    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
72-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:5-80
72-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:22-77
73    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
73-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:5-75
73-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:22-72
74    <uses-permission android:name="android.permission.READ_PROFILE" />
74-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:5-71
74-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:22-68
75    <uses-permission android:name="android.permission.READ_SMS" />
75-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:5-67
75-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:22-64
76    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
76-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:5-77
76-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:22-74
77    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
77-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:5-77
77-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:22-74
78    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
78-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:5-74
78-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:22-71
79    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
79-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:5-79
79-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:22-76
80    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
80-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:5-81
80-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:22-78
81    <uses-permission android:name="android.permission.RECEIVE_MMS" />
81-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:5-70
81-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:22-67
82    <uses-permission android:name="android.permission.RECEIVE_SMS" />
82-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:5-70
82-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:22-67
83    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
83-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:5-75
83-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:22-72
84    <uses-permission android:name="android.permission.RECORD_AUDIO" />
84-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:5-71
84-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:22-68
85    <uses-permission android:name="android.permission.REORDER_TASKS" />
85-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:5-72
85-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:22-69
86    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
86-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:5-75
86-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:22-72
87    <uses-permission android:name="android.permission.SEND_SMS" />
87-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:5-67
87-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:22-64
88    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
88-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:5-72
88-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:22-69
89    <uses-permission android:name="android.permission.SET_WALLPAPER" />
89-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:5-72
89-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:22-69
90    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
90-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:5-78
90-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:22-75
91    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
91-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:5-80
91-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:22-77
92    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
92-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:5-81
92-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:22-78
93    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
93-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:5-78
93-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:22-75
94    <uses-permission android:name="android.permission.TRANSMIT_IR" />
94-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:5-70
94-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:22-67
95    <uses-permission android:name="android.permission.USE_SIP" />
95-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:5-66
95-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:22-63
96    <uses-permission android:name="android.permission.VIBRATE" />
96-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:5-66
96-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:22-63
97    <uses-permission android:name="android.permission.WAKE_LOCK" />
97-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:5-68
97-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:22-65
98    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
98-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:5-73
98-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:22-70
99    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
99-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:5-73
99-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:22-70
100    <uses-permission android:name="android.permission.WRITE_CLIPS" />
100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:5-70
100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:22-67
101    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:5-73
101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:22-70
102    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:5-81
102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:22-78
103    <uses-permission android:name="android.permission.WRITE_PROFILE" />
103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:5-72
103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:22-69
104    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:5-73
104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:22-70
105    <uses-permission android:name="android.permission.WRITE_SMS" />
105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:5-68
105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:22-65
106    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:5-78
106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:22-75
107    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:5-78
107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:22-75
108    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:5-80
108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:22-77
109    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:5-74
109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:22-71
110    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:5-78
110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:22-75
111    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:5-93
111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:22-90
112    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:5-94
112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:22-91
113    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:5-88
113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:22-85
114    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:5-90
114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:22-87
115    <uses-permission android:name="com.android.vending.BILLING" />
115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:5-67
115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:22-64
116    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:5-73
116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:22-70
117    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:5-86
117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:22-83
118    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:5-82
118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:22-79
119    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:5-94
119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:22-91
120    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:5-92
120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:22-89
121    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:5-92
121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:22-89
122    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:5-107
122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:22-104
123    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:5-104
123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:22-101
124    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adsense" />
124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:5-100
124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:22-97
125    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adwords" />
125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:5-100
125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:22-97
126    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ah" />
126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:5-95
126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:22-92
127    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.android" />
127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:5-100
127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:22-97
128    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.androidsecure" />
128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:5-106
128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:22-103
129    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.blogger" />
129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:5-100
129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:22-97
130    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cl" />
130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:5-95
130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:22-92
131    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cp" />
131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:5-95
131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:22-92
132    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.dodgeball" />
132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:5-102
132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:22-99
133    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.finance" />
133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:5-100
133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:22-97
134    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.gbase" />
134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:5-98
134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:22-95
135    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.grandcentral" />
135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:5-105
135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:22-102
136    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.groups2" />
136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:141:5-100
136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:141:22-97
137    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.health" />
137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:142:5-99
137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:142:22-96
138    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ig" />
138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:5-95
138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:22-92
139    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.jotspot" />
139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:5-100
139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:22-97
140    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.knol" />
140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:5-97
140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:22-94
141    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.lh2" />
141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:5-96
141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:22-93
142    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.local" />
142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:5-98
142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:22-95
143    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mail" />
143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:5-97
143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:22-94
144    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mobile" />
144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:5-99
144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:22-96
145    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.news" />
145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:5-97
145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:22-94
146    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.notebook" />
146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:5-101
146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:22-98
147    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.orkut" />
147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:5-98
147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:22-95
148    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.print" />
148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:5-98
148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:22-95
149    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierra" />
149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:5-99
149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:22-96
150    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierraqa" />
150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:5-101
150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:22-98
151    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierrasandbox" />
151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:5-106
151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:22-103
152    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sitemaps" />
152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:5-101
152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:22-98
153    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speech" />
153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:5-99
153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:22-96
154    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speechpersonalization" />
154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:5-114
154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:22-111
155    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.talk" />
155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:5-97
155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:22-94
156    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wifi" />
156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:5-97
156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:22-94
157    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wise" />
157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:5-97
157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:22-94
158    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.writely" />
158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:5-100
158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:22-97
159    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.youtube" />
159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:5-100
159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:22-97
160    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:5-92
160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:22-89
161    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:5-98
161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:22-95
162    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:5-94
162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:22-91
163    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:5-95
163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:22-92
164    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:5-84
164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:22-81
165    <uses-permission android:name="android.permission.READ_LOGS" />
165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:5-68
165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:22-65
166    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:171:5-173:47
166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:172:9-59
167    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:174:5-176:47
167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:175:9-58
168    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:177:5-179:47
168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:178:9-62
169    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:180:5-182:47
169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:181:9-62
170    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:183:5-185:47
170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:184:9-66
171    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:5-74
171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:22-71
172    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:5-75
172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:22-72
173    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:5-79
173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:22-76
174    <uses-permission android:name="android.permission.DEVICE_POWER" />
174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:189:5-191:47
174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:190:9-55
175    <uses-permission android:name="android.permission.BATTERY_STATS" />
175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:5-72
175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:22-69
176    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
176-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:5-82
176-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:22-79
177    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
177-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:5-85
177-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:22-82
178    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
178-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:5-86
178-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:22-83
179    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
179-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:5-86
179-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:22-83
180    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
180-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:5-86
180-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:22-83
181    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
181-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:198:5-89
181-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:198:22-86
182    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
182-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:199:5-93
182-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:199:22-90
183    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
183-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:200:5-80
183-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:200:22-77
184    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
184-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:201:5-85
184-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:201:22-82
185    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
185-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:202:5-86
185-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:202:22-83
186    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
186-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:203:5-78
186-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:203:22-75
187    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
187-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:204:5-81
187-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:204:22-78
188    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
188-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:205:5-84
188-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:205:22-81
189    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
189-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:206:5-82
189-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:206:22-79
190    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
190-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:207:5-82
190-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:207:22-79
191    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
191-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:208:5-79
191-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:208:22-76
192    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
192-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:209:5-82
192-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:209:22-79
193    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
193-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:210:5-92
193-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:210:22-89
194    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
194-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:211:5-91
194-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:211:22-88
195    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
195-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:212:5-83
195-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:212:22-80
196    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />
196-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:213:5-215:47
196-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:214:9-61
197
198    <uses-feature android:name="android.hardware.camera" />
198-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:217:5-60
198-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:217:19-57
199    <uses-feature
199-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:218:5-220:36
200        android:name="android.hardware.camera.autofocus"
200-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:219:9-57
201        android:required="false" />
201-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:220:9-33
202
203    <permission
203-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
204        android:name="io.virtualapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
204-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
205        android:protectionLevel="signature" />
205-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
206
207    <uses-permission android:name="io.virtualapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
207-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
207-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
208
209    <application
209-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:5:5-54:19
210        android:name="io.virtualapp.VApp"
210-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:6:9-29
211        android:allowBackup="true"
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:7:9-35
212        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
212-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
213        android:debuggable="true"
214        android:extractNativeLibs="true"
215        android:icon="@mipmap/ic_launcher"
215-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:8:9-43
216        android:label="@string/app_name"
216-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:9:9-41
217        android:theme="@style/AppTheme" >
217-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:10:9-40
218        <meta-data
218-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:11:9-13:66
219            android:name="TencentMapSDK"
219-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:12:13-41
220            android:value="4HPBZ-2QWC6-H47SR-M6PZY-MTZB5-N2F4F" />
220-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:13:13-64
221
222        <activity
222-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:15:9-24:20
223            android:name="io.virtualapp.splash.SplashActivity"
223-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:16:13-50
224            android:exported="true"
224-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:19:13-36
225            android:screenOrientation="portrait"
225-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:17:13-49
226            android:theme="@style/AppTheme" >
226-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:18:13-44
227            <intent-filter>
227-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:20:13-23:29
228                <action android:name="android.intent.action.MAIN" />
228-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:21:17-68
228-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:21:25-66
229
230                <category android:name="android.intent.category.LAUNCHER" />
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:22:17-76
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:22:27-74
231            </intent-filter>
232        </activity>
233        <activity
233-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:26:9-29:45
234            android:name="io.virtualapp.home.HomeActivity"
234-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:27:13-46
235            android:screenOrientation="portrait"
235-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:28:13-49
236            android:theme="@style/UITheme" />
236-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:29:13-43
237        <activity
237-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:31:9-34:45
238            android:name="io.virtualapp.home.ListAppActivity"
238-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:32:13-49
239            android:screenOrientation="portrait"
239-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:33:13-49
240            android:theme="@style/UITheme" />
240-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:34:13-43
241        <activity
241-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:36:9-42:54
242            android:name="io.virtualapp.home.LoadingActivity"
242-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:37:13-49
243            android:excludeFromRecents="true"
243-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:38:13-46
244            android:noHistory="true"
244-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:39:13-37
245            android:screenOrientation="portrait"
245-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:40:13-49
246            android:taskAffinity="va.task.loading"
246-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:41:13-51
247            android:theme="@style/TransparentTheme" />
247-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:42:13-52
248        <activity
248-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:43:9-47:15
249            android:name="io.virtualapp.home.location.VirtualLocationSettings"
249-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:44:13-66
250            android:screenOrientation="portrait"
250-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:45:13-49
251            android:theme="@style/UITheme" />
251-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:46:13-43
252        <activity
252-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:49:9-53:15
253            android:name="io.virtualapp.home.location.MarkerActivity"
253-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:50:13-57
254            android:screenOrientation="portrait"
254-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:51:13-49
255            android:theme="@style/UITheme" />
255-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:52:13-43
256
257        <service
257-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:223:9-225:61
258            android:name="com.lody.virtual.client.stub.DaemonService"
258-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:224:13-70
259            android:process="@string/engine_process_name" />
259-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:225:13-58
260        <service
260-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:226:9-228:61
261            android:name="com.lody.virtual.client.stub.DaemonService$InnerService"
261-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:227:13-83
262            android:process="@string/engine_process_name" />
262-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:228:13-58
263
264        <activity
264-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:230:9-234:75
265            android:name="com.lody.virtual.client.stub.ShortcutHandleActivity"
265-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:231:13-79
266            android:exported="true"
266-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:232:13-36
267            android:process="@string/engine_process_name"
267-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:233:13-58
268            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
268-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:234:13-72
269        <activity
269-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:235:9-237:61
270            android:name="com.lody.virtual.client.stub.StubPendingActivity"
270-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:236:13-76
271            android:process="@string/engine_process_name" />
271-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:237:13-58
272
273        <service
273-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:239:9-241:61
274            android:name="com.lody.virtual.client.stub.StubPendingService"
274-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:240:13-75
275            android:process="@string/engine_process_name" />
275-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:241:13-58
276
277        <receiver
277-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:243:9-245:61
278            android:name="com.lody.virtual.client.stub.StubPendingReceiver"
278-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:244:13-76
279            android:process="@string/engine_process_name" />
279-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:245:13-58
280
281        <service
281-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:247:9-250:61
282            android:name="com.lody.virtual.client.stub.StubJob"
282-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:248:13-64
283            android:permission="android.permission.BIND_JOB_SERVICE"
283-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:249:13-69
284            android:process="@string/engine_process_name" />
284-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:250:13-58
285
286        <activity
286-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:252:9-258:52
287            android:name="com.lody.virtual.client.stub.ChooseAccountTypeActivity"
287-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:253:13-82
288            android:configChanges="keyboard|keyboardHidden|orientation"
288-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:254:13-72
289            android:excludeFromRecents="true"
289-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:255:13-46
290            android:exported="false"
290-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:256:13-37
291            android:process="@string/engine_process_name"
291-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:257:13-58
292            android:screenOrientation="portrait" />
292-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:258:13-49
293        <activity
293-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:259:9-265:52
294            android:name="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"
294-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:260:13-85
295            android:configChanges="keyboard|keyboardHidden|orientation"
295-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:261:13-72
296            android:excludeFromRecents="true"
296-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:262:13-46
297            android:exported="false"
297-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:263:13-37
298            android:process="@string/engine_process_name"
298-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:264:13-58
299            android:screenOrientation="portrait" />
299-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:265:13-49
300        <activity
300-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:266:9-274:51
301            android:name="com.lody.virtual.client.stub.ChooserActivity"
301-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:267:13-72
302            android:configChanges="keyboard|keyboardHidden|orientation"
302-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:268:13-72
303            android:excludeFromRecents="true"
303-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:269:13-46
304            android:exported="true"
304-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:270:13-36
305            android:finishOnCloseSystemDialogs="true"
305-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:271:13-54
306            android:process="@string/engine_process_name"
306-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:272:13-58
307            android:screenOrientation="portrait"
307-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:273:13-49
308            android:theme="@style/VAAlertTheme" />
308-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:274:13-48
309        <activity
309-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:275:9-283:51
310            android:name="com.lody.virtual.client.stub.ResolverActivity"
310-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:276:13-73
311            android:configChanges="keyboard|keyboardHidden|orientation"
311-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:277:13-72
312            android:excludeFromRecents="true"
312-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:278:13-46
313            android:exported="true"
313-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:279:13-36
314            android:finishOnCloseSystemDialogs="true"
314-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:280:13-54
315            android:process="@string/engine_process_name"
315-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:281:13-58
316            android:screenOrientation="portrait"
316-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:282:13-49
317            android:theme="@style/VAAlertTheme" />
317-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:283:13-48
318
319        <provider
319-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:285:9-289:61
320            android:name="com.lody.virtual.server.BinderProvider"
320-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:286:13-66
321            android:authorities="io.virtualapp.virtual.service.BinderProvider"
321-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:287:13-82
322            android:exported="false"
322-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:288:13-37
323            android:process="@string/engine_process_name" />
323-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:289:13-58
324
325        <activity
325-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:291:9-296:46
326            android:name="com.lody.virtual.client.stub.StubActivity$C0"
326-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:292:13-72
327            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
327-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:293:13-170
328            android:process=":p0"
328-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:294:13-34
329            android:taskAffinity="com.lody.virtual.vt"
329-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:295:13-55
330            android:theme="@style/VATheme" />
330-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:296:13-43
331        <activity
331-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:297:9-302:46
332            android:name="com.lody.virtual.client.stub.StubActivity$C1"
332-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:298:13-72
333            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
333-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:299:13-170
334            android:process=":p1"
334-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:300:13-34
335            android:taskAffinity="com.lody.virtual.vt"
335-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:301:13-55
336            android:theme="@style/VATheme" />
336-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:302:13-43
337        <activity
337-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:303:9-308:46
338            android:name="com.lody.virtual.client.stub.StubActivity$C2"
338-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:304:13-72
339            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
339-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:305:13-170
340            android:process=":p2"
340-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:306:13-34
341            android:taskAffinity="com.lody.virtual.vt"
341-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:307:13-55
342            android:theme="@style/VATheme" />
342-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:308:13-43
343        <activity
343-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:309:9-314:46
344            android:name="com.lody.virtual.client.stub.StubActivity$C3"
344-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:310:13-72
345            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
345-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:311:13-170
346            android:process=":p3"
346-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:312:13-34
347            android:taskAffinity="com.lody.virtual.vt"
347-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:313:13-55
348            android:theme="@style/VATheme" />
348-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:314:13-43
349        <activity
349-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:315:9-320:46
350            android:name="com.lody.virtual.client.stub.StubActivity$C4"
350-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:316:13-72
351            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
351-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:317:13-170
352            android:process=":p4"
352-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:318:13-34
353            android:taskAffinity="com.lody.virtual.vt"
353-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:319:13-55
354            android:theme="@style/VATheme" />
354-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:320:13-43
355        <activity
355-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:321:9-326:46
356            android:name="com.lody.virtual.client.stub.StubActivity$C5"
356-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:322:13-72
357            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
357-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:323:13-170
358            android:process=":p5"
358-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:324:13-34
359            android:taskAffinity="com.lody.virtual.vt"
359-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:325:13-55
360            android:theme="@style/VATheme" />
360-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:326:13-43
361        <activity
361-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:327:9-332:46
362            android:name="com.lody.virtual.client.stub.StubActivity$C6"
362-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:328:13-72
363            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
363-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:329:13-170
364            android:process=":p6"
364-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:330:13-34
365            android:taskAffinity="com.lody.virtual.vt"
365-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:331:13-55
366            android:theme="@style/VATheme" />
366-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:332:13-43
367        <activity
367-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:333:9-338:46
368            android:name="com.lody.virtual.client.stub.StubActivity$C7"
368-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:334:13-72
369            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
369-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:335:13-170
370            android:process=":p7"
370-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:336:13-34
371            android:taskAffinity="com.lody.virtual.vt"
371-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:337:13-55
372            android:theme="@style/VATheme" />
372-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:338:13-43
373        <activity
373-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:339:9-344:46
374            android:name="com.lody.virtual.client.stub.StubActivity$C8"
374-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:340:13-72
375            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
375-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:341:13-170
376            android:process=":p8"
376-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:342:13-34
377            android:taskAffinity="com.lody.virtual.vt"
377-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:343:13-55
378            android:theme="@style/VATheme" />
378-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:344:13-43
379        <activity
379-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:345:9-350:46
380            android:name="com.lody.virtual.client.stub.StubActivity$C9"
380-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:346:13-72
381            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
381-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:347:13-170
382            android:process=":p9"
382-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:348:13-34
383            android:taskAffinity="com.lody.virtual.vt"
383-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:349:13-55
384            android:theme="@style/VATheme" />
384-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:350:13-43
385        <activity
385-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:351:9-356:46
386            android:name="com.lody.virtual.client.stub.StubActivity$C10"
386-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:352:13-73
387            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
387-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:353:13-170
388            android:process=":p10"
388-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:354:13-35
389            android:taskAffinity="com.lody.virtual.vt"
389-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:355:13-55
390            android:theme="@style/VATheme" />
390-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:356:13-43
391        <activity
391-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:357:9-362:46
392            android:name="com.lody.virtual.client.stub.StubActivity$C11"
392-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:358:13-73
393            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
393-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:359:13-170
394            android:process=":p11"
394-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:360:13-35
395            android:taskAffinity="com.lody.virtual.vt"
395-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:361:13-55
396            android:theme="@style/VATheme" />
396-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:362:13-43
397        <activity
397-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:363:9-368:46
398            android:name="com.lody.virtual.client.stub.StubActivity$C12"
398-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:364:13-73
399            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
399-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:365:13-170
400            android:process=":p12"
400-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:366:13-35
401            android:taskAffinity="com.lody.virtual.vt"
401-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:367:13-55
402            android:theme="@style/VATheme" />
402-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:368:13-43
403        <activity
403-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:369:9-374:46
404            android:name="com.lody.virtual.client.stub.StubActivity$C13"
404-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:370:13-73
405            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
405-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:371:13-170
406            android:process=":p13"
406-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:372:13-35
407            android:taskAffinity="com.lody.virtual.vt"
407-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:373:13-55
408            android:theme="@style/VATheme" />
408-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:374:13-43
409        <activity
409-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:375:9-380:46
410            android:name="com.lody.virtual.client.stub.StubActivity$C14"
410-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:376:13-73
411            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
411-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:377:13-170
412            android:process=":p14"
412-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:378:13-35
413            android:taskAffinity="com.lody.virtual.vt"
413-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:379:13-55
414            android:theme="@style/VATheme" />
414-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:380:13-43
415        <activity
415-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:381:9-386:46
416            android:name="com.lody.virtual.client.stub.StubActivity$C15"
416-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:382:13-73
417            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
417-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:383:13-170
418            android:process=":p15"
418-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:384:13-35
419            android:taskAffinity="com.lody.virtual.vt"
419-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:385:13-55
420            android:theme="@style/VATheme" />
420-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:386:13-43
421        <activity
421-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:387:9-392:46
422            android:name="com.lody.virtual.client.stub.StubActivity$C16"
422-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:388:13-73
423            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
423-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:389:13-170
424            android:process=":p16"
424-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:390:13-35
425            android:taskAffinity="com.lody.virtual.vt"
425-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:391:13-55
426            android:theme="@style/VATheme" />
426-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:392:13-43
427        <activity
427-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:393:9-398:46
428            android:name="com.lody.virtual.client.stub.StubActivity$C17"
428-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:394:13-73
429            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
429-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:395:13-170
430            android:process=":p17"
430-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:396:13-35
431            android:taskAffinity="com.lody.virtual.vt"
431-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:397:13-55
432            android:theme="@style/VATheme" />
432-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:398:13-43
433        <activity
433-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:399:9-404:46
434            android:name="com.lody.virtual.client.stub.StubActivity$C18"
434-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:400:13-73
435            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
435-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:401:13-170
436            android:process=":p18"
436-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:402:13-35
437            android:taskAffinity="com.lody.virtual.vt"
437-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:403:13-55
438            android:theme="@style/VATheme" />
438-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:404:13-43
439        <activity
439-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:405:9-410:46
440            android:name="com.lody.virtual.client.stub.StubActivity$C19"
440-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:406:13-73
441            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
441-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:407:13-170
442            android:process=":p19"
442-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:408:13-35
443            android:taskAffinity="com.lody.virtual.vt"
443-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:409:13-55
444            android:theme="@style/VATheme" />
444-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:410:13-43
445        <activity
445-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:411:9-416:46
446            android:name="com.lody.virtual.client.stub.StubActivity$C20"
446-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:412:13-73
447            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
447-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:413:13-170
448            android:process=":p20"
448-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:414:13-35
449            android:taskAffinity="com.lody.virtual.vt"
449-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:415:13-55
450            android:theme="@style/VATheme" />
450-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:416:13-43
451        <activity
451-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:417:9-422:46
452            android:name="com.lody.virtual.client.stub.StubActivity$C21"
452-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:418:13-73
453            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
453-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:419:13-170
454            android:process=":p21"
454-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:420:13-35
455            android:taskAffinity="com.lody.virtual.vt"
455-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:421:13-55
456            android:theme="@style/VATheme" />
456-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:422:13-43
457        <activity
457-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:423:9-428:46
458            android:name="com.lody.virtual.client.stub.StubActivity$C22"
458-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:424:13-73
459            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
459-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:425:13-170
460            android:process=":p22"
460-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:426:13-35
461            android:taskAffinity="com.lody.virtual.vt"
461-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:427:13-55
462            android:theme="@style/VATheme" />
462-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:428:13-43
463        <activity
463-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:429:9-434:46
464            android:name="com.lody.virtual.client.stub.StubActivity$C23"
464-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:430:13-73
465            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
465-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:431:13-170
466            android:process=":p23"
466-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:432:13-35
467            android:taskAffinity="com.lody.virtual.vt"
467-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:433:13-55
468            android:theme="@style/VATheme" />
468-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:434:13-43
469        <activity
469-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:435:9-440:46
470            android:name="com.lody.virtual.client.stub.StubActivity$C24"
470-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:436:13-73
471            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
471-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:437:13-170
472            android:process=":p24"
472-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:438:13-35
473            android:taskAffinity="com.lody.virtual.vt"
473-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:439:13-55
474            android:theme="@style/VATheme" />
474-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:440:13-43
475        <activity
475-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:441:9-446:46
476            android:name="com.lody.virtual.client.stub.StubActivity$C25"
476-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:442:13-73
477            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
477-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:443:13-170
478            android:process=":p25"
478-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:444:13-35
479            android:taskAffinity="com.lody.virtual.vt"
479-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:445:13-55
480            android:theme="@style/VATheme" />
480-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:446:13-43
481        <activity
481-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:447:9-452:46
482            android:name="com.lody.virtual.client.stub.StubActivity$C26"
482-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:448:13-73
483            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
483-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:449:13-170
484            android:process=":p26"
484-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:450:13-35
485            android:taskAffinity="com.lody.virtual.vt"
485-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:451:13-55
486            android:theme="@style/VATheme" />
486-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:452:13-43
487        <activity
487-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:453:9-458:46
488            android:name="com.lody.virtual.client.stub.StubActivity$C27"
488-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:454:13-73
489            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
489-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:455:13-170
490            android:process=":p27"
490-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:456:13-35
491            android:taskAffinity="com.lody.virtual.vt"
491-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:457:13-55
492            android:theme="@style/VATheme" />
492-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:458:13-43
493        <activity
493-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:459:9-464:46
494            android:name="com.lody.virtual.client.stub.StubActivity$C28"
494-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:460:13-73
495            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
495-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:461:13-170
496            android:process=":p28"
496-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:462:13-35
497            android:taskAffinity="com.lody.virtual.vt"
497-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:463:13-55
498            android:theme="@style/VATheme" />
498-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:464:13-43
499        <activity
499-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:465:9-470:46
500            android:name="com.lody.virtual.client.stub.StubActivity$C29"
500-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:466:13-73
501            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
501-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:467:13-170
502            android:process=":p29"
502-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:468:13-35
503            android:taskAffinity="com.lody.virtual.vt"
503-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:469:13-55
504            android:theme="@style/VATheme" />
504-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:470:13-43
505        <activity
505-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:471:9-476:46
506            android:name="com.lody.virtual.client.stub.StubActivity$C30"
506-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:472:13-73
507            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
507-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:473:13-170
508            android:process=":p30"
508-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:474:13-35
509            android:taskAffinity="com.lody.virtual.vt"
509-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:475:13-55
510            android:theme="@style/VATheme" />
510-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:476:13-43
511        <activity
511-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:477:9-482:46
512            android:name="com.lody.virtual.client.stub.StubActivity$C31"
512-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:478:13-73
513            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
513-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:479:13-170
514            android:process=":p31"
514-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:480:13-35
515            android:taskAffinity="com.lody.virtual.vt"
515-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:481:13-55
516            android:theme="@style/VATheme" />
516-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:482:13-43
517        <activity
517-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:483:9-488:46
518            android:name="com.lody.virtual.client.stub.StubActivity$C32"
518-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:484:13-73
519            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
519-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:485:13-170
520            android:process=":p32"
520-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:486:13-35
521            android:taskAffinity="com.lody.virtual.vt"
521-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:487:13-55
522            android:theme="@style/VATheme" />
522-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:488:13-43
523        <activity
523-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:489:9-494:46
524            android:name="com.lody.virtual.client.stub.StubActivity$C33"
524-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:490:13-73
525            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
525-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:491:13-170
526            android:process=":p33"
526-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:492:13-35
527            android:taskAffinity="com.lody.virtual.vt"
527-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:493:13-55
528            android:theme="@style/VATheme" />
528-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:494:13-43
529        <activity
529-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:495:9-500:46
530            android:name="com.lody.virtual.client.stub.StubActivity$C34"
530-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:496:13-73
531            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
531-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:497:13-170
532            android:process=":p34"
532-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:498:13-35
533            android:taskAffinity="com.lody.virtual.vt"
533-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:499:13-55
534            android:theme="@style/VATheme" />
534-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:500:13-43
535        <activity
535-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:501:9-506:46
536            android:name="com.lody.virtual.client.stub.StubActivity$C35"
536-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:502:13-73
537            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
537-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:503:13-170
538            android:process=":p35"
538-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:504:13-35
539            android:taskAffinity="com.lody.virtual.vt"
539-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:505:13-55
540            android:theme="@style/VATheme" />
540-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:506:13-43
541        <activity
541-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:507:9-512:46
542            android:name="com.lody.virtual.client.stub.StubActivity$C36"
542-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:508:13-73
543            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
543-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:509:13-170
544            android:process=":p36"
544-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:510:13-35
545            android:taskAffinity="com.lody.virtual.vt"
545-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:511:13-55
546            android:theme="@style/VATheme" />
546-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:512:13-43
547        <activity
547-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:513:9-518:46
548            android:name="com.lody.virtual.client.stub.StubActivity$C37"
548-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:514:13-73
549            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
549-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:515:13-170
550            android:process=":p37"
550-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:516:13-35
551            android:taskAffinity="com.lody.virtual.vt"
551-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:517:13-55
552            android:theme="@style/VATheme" />
552-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:518:13-43
553        <activity
553-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:519:9-524:46
554            android:name="com.lody.virtual.client.stub.StubActivity$C38"
554-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:520:13-73
555            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
555-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:521:13-170
556            android:process=":p38"
556-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:522:13-35
557            android:taskAffinity="com.lody.virtual.vt"
557-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:523:13-55
558            android:theme="@style/VATheme" />
558-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:524:13-43
559        <activity
559-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:525:9-530:46
560            android:name="com.lody.virtual.client.stub.StubActivity$C39"
560-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:526:13-73
561            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
561-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:527:13-170
562            android:process=":p39"
562-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:528:13-35
563            android:taskAffinity="com.lody.virtual.vt"
563-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:529:13-55
564            android:theme="@style/VATheme" />
564-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:530:13-43
565        <activity
565-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:531:9-536:46
566            android:name="com.lody.virtual.client.stub.StubActivity$C40"
566-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:532:13-73
567            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
567-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:533:13-170
568            android:process=":p40"
568-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:534:13-35
569            android:taskAffinity="com.lody.virtual.vt"
569-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:535:13-55
570            android:theme="@style/VATheme" />
570-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:536:13-43
571        <activity
571-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:537:9-542:46
572            android:name="com.lody.virtual.client.stub.StubActivity$C41"
572-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:538:13-73
573            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
573-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:539:13-170
574            android:process=":p41"
574-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:540:13-35
575            android:taskAffinity="com.lody.virtual.vt"
575-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:541:13-55
576            android:theme="@style/VATheme" />
576-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:542:13-43
577        <activity
577-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:543:9-548:46
578            android:name="com.lody.virtual.client.stub.StubActivity$C42"
578-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:544:13-73
579            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
579-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:545:13-170
580            android:process=":p42"
580-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:546:13-35
581            android:taskAffinity="com.lody.virtual.vt"
581-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:547:13-55
582            android:theme="@style/VATheme" />
582-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:548:13-43
583        <activity
583-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:549:9-554:46
584            android:name="com.lody.virtual.client.stub.StubActivity$C43"
584-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:550:13-73
585            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
585-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:551:13-170
586            android:process=":p43"
586-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:552:13-35
587            android:taskAffinity="com.lody.virtual.vt"
587-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:553:13-55
588            android:theme="@style/VATheme" />
588-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:554:13-43
589        <activity
589-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:555:9-560:46
590            android:name="com.lody.virtual.client.stub.StubActivity$C44"
590-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:556:13-73
591            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
591-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:557:13-170
592            android:process=":p44"
592-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:558:13-35
593            android:taskAffinity="com.lody.virtual.vt"
593-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:559:13-55
594            android:theme="@style/VATheme" />
594-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:560:13-43
595        <activity
595-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:561:9-566:46
596            android:name="com.lody.virtual.client.stub.StubActivity$C45"
596-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:562:13-73
597            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
597-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:563:13-170
598            android:process=":p45"
598-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:564:13-35
599            android:taskAffinity="com.lody.virtual.vt"
599-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:565:13-55
600            android:theme="@style/VATheme" />
600-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:566:13-43
601        <activity
601-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:567:9-572:46
602            android:name="com.lody.virtual.client.stub.StubActivity$C46"
602-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:568:13-73
603            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
603-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:569:13-170
604            android:process=":p46"
604-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:570:13-35
605            android:taskAffinity="com.lody.virtual.vt"
605-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:571:13-55
606            android:theme="@style/VATheme" />
606-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:572:13-43
607        <activity
607-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:573:9-578:46
608            android:name="com.lody.virtual.client.stub.StubActivity$C47"
608-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:574:13-73
609            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
609-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:575:13-170
610            android:process=":p47"
610-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:576:13-35
611            android:taskAffinity="com.lody.virtual.vt"
611-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:577:13-55
612            android:theme="@style/VATheme" />
612-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:578:13-43
613        <activity
613-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:579:9-584:46
614            android:name="com.lody.virtual.client.stub.StubActivity$C48"
614-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:580:13-73
615            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
615-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:581:13-170
616            android:process=":p48"
616-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:582:13-35
617            android:taskAffinity="com.lody.virtual.vt"
617-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:583:13-55
618            android:theme="@style/VATheme" />
618-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:584:13-43
619        <activity
619-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:585:9-590:46
620            android:name="com.lody.virtual.client.stub.StubActivity$C49"
620-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:586:13-73
621            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
621-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:587:13-170
622            android:process=":p49"
622-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:588:13-35
623            android:taskAffinity="com.lody.virtual.vt"
623-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:589:13-55
624            android:theme="@style/VATheme" />
624-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:590:13-43
625        <activity
625-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:591:9-596:59
626            android:name="com.lody.virtual.client.stub.StubDialog$C0"
626-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:592:13-70
627            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
627-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:593:13-170
628            android:process=":p0"
628-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:594:13-34
629            android:taskAffinity="com.lody.virtual.vt"
629-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:595:13-55
630            android:theme="@android:style/Theme.Dialog" />
630-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:596:13-56
631        <activity
631-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:597:9-602:59
632            android:name="com.lody.virtual.client.stub.StubDialog$C1"
632-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:598:13-70
633            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
633-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:599:13-170
634            android:process=":p1"
634-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:600:13-34
635            android:taskAffinity="com.lody.virtual.vt"
635-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:601:13-55
636            android:theme="@android:style/Theme.Dialog" />
636-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:602:13-56
637        <activity
637-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:603:9-608:59
638            android:name="com.lody.virtual.client.stub.StubDialog$C2"
638-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:604:13-70
639            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
639-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:605:13-170
640            android:process=":p2"
640-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:606:13-34
641            android:taskAffinity="com.lody.virtual.vt"
641-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:607:13-55
642            android:theme="@android:style/Theme.Dialog" />
642-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:608:13-56
643        <activity
643-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:609:9-614:59
644            android:name="com.lody.virtual.client.stub.StubDialog$C3"
644-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:610:13-70
645            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
645-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:611:13-170
646            android:process=":p3"
646-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:612:13-34
647            android:taskAffinity="com.lody.virtual.vt"
647-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:613:13-55
648            android:theme="@android:style/Theme.Dialog" />
648-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:614:13-56
649        <activity
649-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:615:9-620:59
650            android:name="com.lody.virtual.client.stub.StubDialog$C4"
650-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:616:13-70
651            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
651-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:617:13-170
652            android:process=":p4"
652-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:618:13-34
653            android:taskAffinity="com.lody.virtual.vt"
653-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:619:13-55
654            android:theme="@android:style/Theme.Dialog" />
654-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:620:13-56
655        <activity
655-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:621:9-626:59
656            android:name="com.lody.virtual.client.stub.StubDialog$C5"
656-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:622:13-70
657            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
657-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:623:13-170
658            android:process=":p5"
658-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:624:13-34
659            android:taskAffinity="com.lody.virtual.vt"
659-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:625:13-55
660            android:theme="@android:style/Theme.Dialog" />
660-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:626:13-56
661        <activity
661-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:627:9-632:59
662            android:name="com.lody.virtual.client.stub.StubDialog$C6"
662-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:628:13-70
663            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
663-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:629:13-170
664            android:process=":p6"
664-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:630:13-34
665            android:taskAffinity="com.lody.virtual.vt"
665-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:631:13-55
666            android:theme="@android:style/Theme.Dialog" />
666-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:632:13-56
667        <activity
667-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:633:9-638:59
668            android:name="com.lody.virtual.client.stub.StubDialog$C7"
668-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:634:13-70
669            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
669-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:635:13-170
670            android:process=":p7"
670-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:636:13-34
671            android:taskAffinity="com.lody.virtual.vt"
671-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:637:13-55
672            android:theme="@android:style/Theme.Dialog" />
672-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:638:13-56
673        <activity
673-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:639:9-644:59
674            android:name="com.lody.virtual.client.stub.StubDialog$C8"
674-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:640:13-70
675            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
675-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:641:13-170
676            android:process=":p8"
676-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:642:13-34
677            android:taskAffinity="com.lody.virtual.vt"
677-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:643:13-55
678            android:theme="@android:style/Theme.Dialog" />
678-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:644:13-56
679        <activity
679-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:645:9-650:59
680            android:name="com.lody.virtual.client.stub.StubDialog$C9"
680-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:646:13-70
681            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
681-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:647:13-170
682            android:process=":p9"
682-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:648:13-34
683            android:taskAffinity="com.lody.virtual.vt"
683-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:649:13-55
684            android:theme="@android:style/Theme.Dialog" />
684-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:650:13-56
685        <activity
685-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:651:9-656:59
686            android:name="com.lody.virtual.client.stub.StubDialog$C10"
686-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:652:13-71
687            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
687-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:653:13-170
688            android:process=":p10"
688-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:654:13-35
689            android:taskAffinity="com.lody.virtual.vt"
689-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:655:13-55
690            android:theme="@android:style/Theme.Dialog" />
690-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:656:13-56
691        <activity
691-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:657:9-662:59
692            android:name="com.lody.virtual.client.stub.StubDialog$C11"
692-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:658:13-71
693            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
693-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:659:13-170
694            android:process=":p11"
694-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:660:13-35
695            android:taskAffinity="com.lody.virtual.vt"
695-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:661:13-55
696            android:theme="@android:style/Theme.Dialog" />
696-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:662:13-56
697        <activity
697-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:663:9-668:59
698            android:name="com.lody.virtual.client.stub.StubDialog$C12"
698-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:664:13-71
699            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
699-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:665:13-170
700            android:process=":p12"
700-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:666:13-35
701            android:taskAffinity="com.lody.virtual.vt"
701-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:667:13-55
702            android:theme="@android:style/Theme.Dialog" />
702-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:668:13-56
703        <activity
703-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:669:9-674:59
704            android:name="com.lody.virtual.client.stub.StubDialog$C13"
704-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:670:13-71
705            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
705-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:671:13-170
706            android:process=":p13"
706-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:672:13-35
707            android:taskAffinity="com.lody.virtual.vt"
707-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:673:13-55
708            android:theme="@android:style/Theme.Dialog" />
708-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:674:13-56
709        <activity
709-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:675:9-680:59
710            android:name="com.lody.virtual.client.stub.StubDialog$C14"
710-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:676:13-71
711            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
711-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:677:13-170
712            android:process=":p14"
712-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:678:13-35
713            android:taskAffinity="com.lody.virtual.vt"
713-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:679:13-55
714            android:theme="@android:style/Theme.Dialog" />
714-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:680:13-56
715        <activity
715-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:681:9-686:59
716            android:name="com.lody.virtual.client.stub.StubDialog$C15"
716-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:682:13-71
717            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
717-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:683:13-170
718            android:process=":p15"
718-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:684:13-35
719            android:taskAffinity="com.lody.virtual.vt"
719-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:685:13-55
720            android:theme="@android:style/Theme.Dialog" />
720-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:686:13-56
721        <activity
721-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:687:9-692:59
722            android:name="com.lody.virtual.client.stub.StubDialog$C16"
722-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:688:13-71
723            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
723-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:689:13-170
724            android:process=":p16"
724-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:690:13-35
725            android:taskAffinity="com.lody.virtual.vt"
725-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:691:13-55
726            android:theme="@android:style/Theme.Dialog" />
726-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:692:13-56
727        <activity
727-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:693:9-698:59
728            android:name="com.lody.virtual.client.stub.StubDialog$C17"
728-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:694:13-71
729            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
729-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:695:13-170
730            android:process=":p17"
730-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:696:13-35
731            android:taskAffinity="com.lody.virtual.vt"
731-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:697:13-55
732            android:theme="@android:style/Theme.Dialog" />
732-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:698:13-56
733        <activity
733-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:699:9-704:59
734            android:name="com.lody.virtual.client.stub.StubDialog$C18"
734-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:700:13-71
735            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
735-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:701:13-170
736            android:process=":p18"
736-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:702:13-35
737            android:taskAffinity="com.lody.virtual.vt"
737-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:703:13-55
738            android:theme="@android:style/Theme.Dialog" />
738-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:704:13-56
739        <activity
739-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:705:9-710:59
740            android:name="com.lody.virtual.client.stub.StubDialog$C19"
740-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:706:13-71
741            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
741-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:707:13-170
742            android:process=":p19"
742-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:708:13-35
743            android:taskAffinity="com.lody.virtual.vt"
743-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:709:13-55
744            android:theme="@android:style/Theme.Dialog" />
744-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:710:13-56
745        <activity
745-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:711:9-716:59
746            android:name="com.lody.virtual.client.stub.StubDialog$C20"
746-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:712:13-71
747            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
747-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:713:13-170
748            android:process=":p20"
748-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:714:13-35
749            android:taskAffinity="com.lody.virtual.vt"
749-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:715:13-55
750            android:theme="@android:style/Theme.Dialog" />
750-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:716:13-56
751        <activity
751-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:717:9-722:59
752            android:name="com.lody.virtual.client.stub.StubDialog$C21"
752-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:718:13-71
753            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
753-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:719:13-170
754            android:process=":p21"
754-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:720:13-35
755            android:taskAffinity="com.lody.virtual.vt"
755-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:721:13-55
756            android:theme="@android:style/Theme.Dialog" />
756-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:722:13-56
757        <activity
757-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:723:9-728:59
758            android:name="com.lody.virtual.client.stub.StubDialog$C22"
758-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:724:13-71
759            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
759-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:725:13-170
760            android:process=":p22"
760-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:726:13-35
761            android:taskAffinity="com.lody.virtual.vt"
761-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:727:13-55
762            android:theme="@android:style/Theme.Dialog" />
762-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:728:13-56
763        <activity
763-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:729:9-734:59
764            android:name="com.lody.virtual.client.stub.StubDialog$C23"
764-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:730:13-71
765            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
765-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:731:13-170
766            android:process=":p23"
766-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:732:13-35
767            android:taskAffinity="com.lody.virtual.vt"
767-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:733:13-55
768            android:theme="@android:style/Theme.Dialog" />
768-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:734:13-56
769        <activity
769-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:735:9-740:59
770            android:name="com.lody.virtual.client.stub.StubDialog$C24"
770-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:736:13-71
771            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
771-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:737:13-170
772            android:process=":p24"
772-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:738:13-35
773            android:taskAffinity="com.lody.virtual.vt"
773-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:739:13-55
774            android:theme="@android:style/Theme.Dialog" />
774-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:740:13-56
775        <activity
775-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:741:9-746:59
776            android:name="com.lody.virtual.client.stub.StubDialog$C25"
776-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:742:13-71
777            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
777-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:743:13-170
778            android:process=":p25"
778-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:744:13-35
779            android:taskAffinity="com.lody.virtual.vt"
779-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:745:13-55
780            android:theme="@android:style/Theme.Dialog" />
780-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:746:13-56
781        <activity
781-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:747:9-752:59
782            android:name="com.lody.virtual.client.stub.StubDialog$C26"
782-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:748:13-71
783            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
783-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:749:13-170
784            android:process=":p26"
784-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:750:13-35
785            android:taskAffinity="com.lody.virtual.vt"
785-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:751:13-55
786            android:theme="@android:style/Theme.Dialog" />
786-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:752:13-56
787        <activity
787-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:753:9-758:59
788            android:name="com.lody.virtual.client.stub.StubDialog$C27"
788-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:754:13-71
789            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
789-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:755:13-170
790            android:process=":p27"
790-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:756:13-35
791            android:taskAffinity="com.lody.virtual.vt"
791-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:757:13-55
792            android:theme="@android:style/Theme.Dialog" />
792-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:758:13-56
793        <activity
793-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:759:9-764:59
794            android:name="com.lody.virtual.client.stub.StubDialog$C28"
794-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:760:13-71
795            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
795-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:761:13-170
796            android:process=":p28"
796-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:762:13-35
797            android:taskAffinity="com.lody.virtual.vt"
797-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:763:13-55
798            android:theme="@android:style/Theme.Dialog" />
798-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:764:13-56
799        <activity
799-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:765:9-770:59
800            android:name="com.lody.virtual.client.stub.StubDialog$C29"
800-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:766:13-71
801            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
801-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:767:13-170
802            android:process=":p29"
802-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:768:13-35
803            android:taskAffinity="com.lody.virtual.vt"
803-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:769:13-55
804            android:theme="@android:style/Theme.Dialog" />
804-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:770:13-56
805        <activity
805-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:771:9-776:59
806            android:name="com.lody.virtual.client.stub.StubDialog$C30"
806-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:772:13-71
807            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
807-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:773:13-170
808            android:process=":p30"
808-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:774:13-35
809            android:taskAffinity="com.lody.virtual.vt"
809-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:775:13-55
810            android:theme="@android:style/Theme.Dialog" />
810-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:776:13-56
811        <activity
811-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:777:9-782:59
812            android:name="com.lody.virtual.client.stub.StubDialog$C31"
812-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:778:13-71
813            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
813-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:779:13-170
814            android:process=":p31"
814-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:780:13-35
815            android:taskAffinity="com.lody.virtual.vt"
815-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:781:13-55
816            android:theme="@android:style/Theme.Dialog" />
816-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:782:13-56
817        <activity
817-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:783:9-788:59
818            android:name="com.lody.virtual.client.stub.StubDialog$C32"
818-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:784:13-71
819            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
819-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:785:13-170
820            android:process=":p32"
820-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:786:13-35
821            android:taskAffinity="com.lody.virtual.vt"
821-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:787:13-55
822            android:theme="@android:style/Theme.Dialog" />
822-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:788:13-56
823        <activity
823-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:789:9-794:59
824            android:name="com.lody.virtual.client.stub.StubDialog$C33"
824-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:790:13-71
825            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
825-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:791:13-170
826            android:process=":p33"
826-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:792:13-35
827            android:taskAffinity="com.lody.virtual.vt"
827-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:793:13-55
828            android:theme="@android:style/Theme.Dialog" />
828-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:794:13-56
829        <activity
829-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:795:9-800:59
830            android:name="com.lody.virtual.client.stub.StubDialog$C34"
830-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:796:13-71
831            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
831-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:797:13-170
832            android:process=":p34"
832-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:798:13-35
833            android:taskAffinity="com.lody.virtual.vt"
833-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:799:13-55
834            android:theme="@android:style/Theme.Dialog" />
834-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:800:13-56
835        <activity
835-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:801:9-806:59
836            android:name="com.lody.virtual.client.stub.StubDialog$C35"
836-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:802:13-71
837            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
837-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:803:13-170
838            android:process=":p35"
838-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:804:13-35
839            android:taskAffinity="com.lody.virtual.vt"
839-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:805:13-55
840            android:theme="@android:style/Theme.Dialog" />
840-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:806:13-56
841        <activity
841-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:807:9-812:59
842            android:name="com.lody.virtual.client.stub.StubDialog$C36"
842-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:808:13-71
843            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
843-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:809:13-170
844            android:process=":p36"
844-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:810:13-35
845            android:taskAffinity="com.lody.virtual.vt"
845-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:811:13-55
846            android:theme="@android:style/Theme.Dialog" />
846-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:812:13-56
847        <activity
847-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:813:9-818:59
848            android:name="com.lody.virtual.client.stub.StubDialog$C37"
848-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:814:13-71
849            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
849-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:815:13-170
850            android:process=":p37"
850-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:816:13-35
851            android:taskAffinity="com.lody.virtual.vt"
851-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:817:13-55
852            android:theme="@android:style/Theme.Dialog" />
852-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:818:13-56
853        <activity
853-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:819:9-824:59
854            android:name="com.lody.virtual.client.stub.StubDialog$C38"
854-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:820:13-71
855            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
855-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:821:13-170
856            android:process=":p38"
856-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:822:13-35
857            android:taskAffinity="com.lody.virtual.vt"
857-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:823:13-55
858            android:theme="@android:style/Theme.Dialog" />
858-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:824:13-56
859        <activity
859-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:825:9-830:59
860            android:name="com.lody.virtual.client.stub.StubDialog$C39"
860-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:826:13-71
861            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
861-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:827:13-170
862            android:process=":p39"
862-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:828:13-35
863            android:taskAffinity="com.lody.virtual.vt"
863-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:829:13-55
864            android:theme="@android:style/Theme.Dialog" />
864-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:830:13-56
865        <activity
865-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:831:9-836:59
866            android:name="com.lody.virtual.client.stub.StubDialog$C40"
866-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:832:13-71
867            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
867-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:833:13-170
868            android:process=":p40"
868-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:834:13-35
869            android:taskAffinity="com.lody.virtual.vt"
869-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:835:13-55
870            android:theme="@android:style/Theme.Dialog" />
870-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:836:13-56
871        <activity
871-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:837:9-842:59
872            android:name="com.lody.virtual.client.stub.StubDialog$C41"
872-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:838:13-71
873            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
873-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:839:13-170
874            android:process=":p41"
874-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:840:13-35
875            android:taskAffinity="com.lody.virtual.vt"
875-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:841:13-55
876            android:theme="@android:style/Theme.Dialog" />
876-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:842:13-56
877        <activity
877-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:843:9-848:59
878            android:name="com.lody.virtual.client.stub.StubDialog$C42"
878-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:844:13-71
879            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
879-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:845:13-170
880            android:process=":p42"
880-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:846:13-35
881            android:taskAffinity="com.lody.virtual.vt"
881-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:847:13-55
882            android:theme="@android:style/Theme.Dialog" />
882-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:848:13-56
883        <activity
883-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:849:9-854:59
884            android:name="com.lody.virtual.client.stub.StubDialog$C43"
884-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:850:13-71
885            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
885-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:851:13-170
886            android:process=":p43"
886-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:852:13-35
887            android:taskAffinity="com.lody.virtual.vt"
887-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:853:13-55
888            android:theme="@android:style/Theme.Dialog" />
888-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:854:13-56
889        <activity
889-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:855:9-860:59
890            android:name="com.lody.virtual.client.stub.StubDialog$C44"
890-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:856:13-71
891            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
891-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:857:13-170
892            android:process=":p44"
892-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:858:13-35
893            android:taskAffinity="com.lody.virtual.vt"
893-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:859:13-55
894            android:theme="@android:style/Theme.Dialog" />
894-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:860:13-56
895        <activity
895-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:861:9-866:59
896            android:name="com.lody.virtual.client.stub.StubDialog$C45"
896-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:862:13-71
897            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
897-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:863:13-170
898            android:process=":p45"
898-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:864:13-35
899            android:taskAffinity="com.lody.virtual.vt"
899-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:865:13-55
900            android:theme="@android:style/Theme.Dialog" />
900-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:866:13-56
901        <activity
901-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:867:9-872:59
902            android:name="com.lody.virtual.client.stub.StubDialog$C46"
902-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:868:13-71
903            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
903-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:869:13-170
904            android:process=":p46"
904-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:870:13-35
905            android:taskAffinity="com.lody.virtual.vt"
905-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:871:13-55
906            android:theme="@android:style/Theme.Dialog" />
906-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:872:13-56
907        <activity
907-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:873:9-878:59
908            android:name="com.lody.virtual.client.stub.StubDialog$C47"
908-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:874:13-71
909            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
909-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:875:13-170
910            android:process=":p47"
910-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:876:13-35
911            android:taskAffinity="com.lody.virtual.vt"
911-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:877:13-55
912            android:theme="@android:style/Theme.Dialog" />
912-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:878:13-56
913        <activity
913-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:879:9-884:59
914            android:name="com.lody.virtual.client.stub.StubDialog$C48"
914-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:880:13-71
915            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
915-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:881:13-170
916            android:process=":p48"
916-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:882:13-35
917            android:taskAffinity="com.lody.virtual.vt"
917-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:883:13-55
918            android:theme="@android:style/Theme.Dialog" />
918-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:884:13-56
919        <activity
919-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:885:9-890:59
920            android:name="com.lody.virtual.client.stub.StubDialog$C49"
920-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:886:13-71
921            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
921-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:887:13-170
922            android:process=":p49"
922-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:888:13-35
923            android:taskAffinity="com.lody.virtual.vt"
923-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:889:13-55
924            android:theme="@android:style/Theme.Dialog" />
924-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:890:13-56
925
926        <provider
926-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:892:9-896:37
927            android:name="com.lody.virtual.client.stub.StubContentProvider$C0"
927-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:893:13-79
928            android:authorities="io.virtualapp.virtual_stub_0"
928-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:894:13-66
929            android:exported="false"
929-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:895:13-37
930            android:process=":p0" />
930-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:896:13-34
931        <provider
931-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:897:9-901:37
932            android:name="com.lody.virtual.client.stub.StubContentProvider$C1"
932-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:898:13-79
933            android:authorities="io.virtualapp.virtual_stub_1"
933-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:899:13-66
934            android:exported="false"
934-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:900:13-37
935            android:process=":p1" />
935-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:901:13-34
936        <provider
936-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:902:9-906:37
937            android:name="com.lody.virtual.client.stub.StubContentProvider$C2"
937-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:903:13-79
938            android:authorities="io.virtualapp.virtual_stub_2"
938-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:904:13-66
939            android:exported="false"
939-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:905:13-37
940            android:process=":p2" />
940-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:906:13-34
941        <provider
941-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:907:9-911:37
942            android:name="com.lody.virtual.client.stub.StubContentProvider$C3"
942-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:908:13-79
943            android:authorities="io.virtualapp.virtual_stub_3"
943-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:909:13-66
944            android:exported="false"
944-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:910:13-37
945            android:process=":p3" />
945-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:911:13-34
946        <provider
946-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:912:9-916:37
947            android:name="com.lody.virtual.client.stub.StubContentProvider$C4"
947-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:913:13-79
948            android:authorities="io.virtualapp.virtual_stub_4"
948-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:914:13-66
949            android:exported="false"
949-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:915:13-37
950            android:process=":p4" />
950-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:916:13-34
951        <provider
951-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:917:9-921:37
952            android:name="com.lody.virtual.client.stub.StubContentProvider$C5"
952-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:918:13-79
953            android:authorities="io.virtualapp.virtual_stub_5"
953-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:919:13-66
954            android:exported="false"
954-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:920:13-37
955            android:process=":p5" />
955-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:921:13-34
956        <provider
956-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:922:9-926:37
957            android:name="com.lody.virtual.client.stub.StubContentProvider$C6"
957-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:923:13-79
958            android:authorities="io.virtualapp.virtual_stub_6"
958-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:924:13-66
959            android:exported="false"
959-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:925:13-37
960            android:process=":p6" />
960-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:926:13-34
961        <provider
961-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:927:9-931:37
962            android:name="com.lody.virtual.client.stub.StubContentProvider$C7"
962-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:928:13-79
963            android:authorities="io.virtualapp.virtual_stub_7"
963-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:929:13-66
964            android:exported="false"
964-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:930:13-37
965            android:process=":p7" />
965-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:931:13-34
966        <provider
966-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:932:9-936:37
967            android:name="com.lody.virtual.client.stub.StubContentProvider$C8"
967-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:933:13-79
968            android:authorities="io.virtualapp.virtual_stub_8"
968-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:934:13-66
969            android:exported="false"
969-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:935:13-37
970            android:process=":p8" />
970-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:936:13-34
971        <provider
971-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:937:9-941:37
972            android:name="com.lody.virtual.client.stub.StubContentProvider$C9"
972-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:938:13-79
973            android:authorities="io.virtualapp.virtual_stub_9"
973-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:939:13-66
974            android:exported="false"
974-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:940:13-37
975            android:process=":p9" />
975-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:941:13-34
976        <provider
976-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:942:9-946:38
977            android:name="com.lody.virtual.client.stub.StubContentProvider$C10"
977-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:943:13-80
978            android:authorities="io.virtualapp.virtual_stub_10"
978-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:944:13-67
979            android:exported="false"
979-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:945:13-37
980            android:process=":p10" />
980-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:946:13-35
981        <provider
981-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:947:9-951:38
982            android:name="com.lody.virtual.client.stub.StubContentProvider$C11"
982-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:948:13-80
983            android:authorities="io.virtualapp.virtual_stub_11"
983-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:949:13-67
984            android:exported="false"
984-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:950:13-37
985            android:process=":p11" />
985-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:951:13-35
986        <provider
986-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:952:9-956:38
987            android:name="com.lody.virtual.client.stub.StubContentProvider$C12"
987-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:953:13-80
988            android:authorities="io.virtualapp.virtual_stub_12"
988-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:954:13-67
989            android:exported="false"
989-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:955:13-37
990            android:process=":p12" />
990-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:956:13-35
991        <provider
991-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:957:9-961:38
992            android:name="com.lody.virtual.client.stub.StubContentProvider$C13"
992-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:958:13-80
993            android:authorities="io.virtualapp.virtual_stub_13"
993-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:959:13-67
994            android:exported="false"
994-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:960:13-37
995            android:process=":p13" />
995-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:961:13-35
996        <provider
996-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:962:9-966:38
997            android:name="com.lody.virtual.client.stub.StubContentProvider$C14"
997-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:963:13-80
998            android:authorities="io.virtualapp.virtual_stub_14"
998-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:964:13-67
999            android:exported="false"
999-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:965:13-37
1000            android:process=":p14" />
1000-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:966:13-35
1001        <provider
1001-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:967:9-971:38
1002            android:name="com.lody.virtual.client.stub.StubContentProvider$C15"
1002-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:968:13-80
1003            android:authorities="io.virtualapp.virtual_stub_15"
1003-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:969:13-67
1004            android:exported="false"
1004-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:970:13-37
1005            android:process=":p15" />
1005-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:971:13-35
1006        <provider
1006-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:972:9-976:38
1007            android:name="com.lody.virtual.client.stub.StubContentProvider$C16"
1007-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:973:13-80
1008            android:authorities="io.virtualapp.virtual_stub_16"
1008-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:974:13-67
1009            android:exported="false"
1009-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:975:13-37
1010            android:process=":p16" />
1010-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:976:13-35
1011        <provider
1011-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:977:9-981:38
1012            android:name="com.lody.virtual.client.stub.StubContentProvider$C17"
1012-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:978:13-80
1013            android:authorities="io.virtualapp.virtual_stub_17"
1013-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:979:13-67
1014            android:exported="false"
1014-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:980:13-37
1015            android:process=":p17" />
1015-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:981:13-35
1016        <provider
1016-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:982:9-986:38
1017            android:name="com.lody.virtual.client.stub.StubContentProvider$C18"
1017-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:983:13-80
1018            android:authorities="io.virtualapp.virtual_stub_18"
1018-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:984:13-67
1019            android:exported="false"
1019-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:985:13-37
1020            android:process=":p18" />
1020-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:986:13-35
1021        <provider
1021-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:987:9-991:38
1022            android:name="com.lody.virtual.client.stub.StubContentProvider$C19"
1022-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:988:13-80
1023            android:authorities="io.virtualapp.virtual_stub_19"
1023-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:989:13-67
1024            android:exported="false"
1024-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:990:13-37
1025            android:process=":p19" />
1025-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:991:13-35
1026        <provider
1026-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:992:9-996:38
1027            android:name="com.lody.virtual.client.stub.StubContentProvider$C20"
1027-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:993:13-80
1028            android:authorities="io.virtualapp.virtual_stub_20"
1028-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:994:13-67
1029            android:exported="false"
1029-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:995:13-37
1030            android:process=":p20" />
1030-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:996:13-35
1031        <provider
1031-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:997:9-1001:38
1032            android:name="com.lody.virtual.client.stub.StubContentProvider$C21"
1032-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:998:13-80
1033            android:authorities="io.virtualapp.virtual_stub_21"
1033-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:999:13-67
1034            android:exported="false"
1034-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1000:13-37
1035            android:process=":p21" />
1035-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1001:13-35
1036        <provider
1036-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1002:9-1006:38
1037            android:name="com.lody.virtual.client.stub.StubContentProvider$C22"
1037-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1003:13-80
1038            android:authorities="io.virtualapp.virtual_stub_22"
1038-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1004:13-67
1039            android:exported="false"
1039-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1005:13-37
1040            android:process=":p22" />
1040-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1006:13-35
1041        <provider
1041-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1007:9-1011:38
1042            android:name="com.lody.virtual.client.stub.StubContentProvider$C23"
1042-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1008:13-80
1043            android:authorities="io.virtualapp.virtual_stub_23"
1043-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1009:13-67
1044            android:exported="false"
1044-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1010:13-37
1045            android:process=":p23" />
1045-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1011:13-35
1046        <provider
1046-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1012:9-1016:38
1047            android:name="com.lody.virtual.client.stub.StubContentProvider$C24"
1047-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1013:13-80
1048            android:authorities="io.virtualapp.virtual_stub_24"
1048-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1014:13-67
1049            android:exported="false"
1049-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1015:13-37
1050            android:process=":p24" />
1050-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1016:13-35
1051        <provider
1051-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1017:9-1021:38
1052            android:name="com.lody.virtual.client.stub.StubContentProvider$C25"
1052-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1018:13-80
1053            android:authorities="io.virtualapp.virtual_stub_25"
1053-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1019:13-67
1054            android:exported="false"
1054-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1020:13-37
1055            android:process=":p25" />
1055-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1021:13-35
1056        <provider
1056-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1022:9-1026:38
1057            android:name="com.lody.virtual.client.stub.StubContentProvider$C26"
1057-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1023:13-80
1058            android:authorities="io.virtualapp.virtual_stub_26"
1058-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1024:13-67
1059            android:exported="false"
1059-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1025:13-37
1060            android:process=":p26" />
1060-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1026:13-35
1061        <provider
1061-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1027:9-1031:38
1062            android:name="com.lody.virtual.client.stub.StubContentProvider$C27"
1062-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1028:13-80
1063            android:authorities="io.virtualapp.virtual_stub_27"
1063-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1029:13-67
1064            android:exported="false"
1064-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1030:13-37
1065            android:process=":p27" />
1065-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1031:13-35
1066        <provider
1066-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1032:9-1036:38
1067            android:name="com.lody.virtual.client.stub.StubContentProvider$C28"
1067-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1033:13-80
1068            android:authorities="io.virtualapp.virtual_stub_28"
1068-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1034:13-67
1069            android:exported="false"
1069-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1035:13-37
1070            android:process=":p28" />
1070-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1036:13-35
1071        <provider
1071-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1037:9-1041:38
1072            android:name="com.lody.virtual.client.stub.StubContentProvider$C29"
1072-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1038:13-80
1073            android:authorities="io.virtualapp.virtual_stub_29"
1073-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1039:13-67
1074            android:exported="false"
1074-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1040:13-37
1075            android:process=":p29" />
1075-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1041:13-35
1076        <provider
1076-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1042:9-1046:38
1077            android:name="com.lody.virtual.client.stub.StubContentProvider$C30"
1077-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1043:13-80
1078            android:authorities="io.virtualapp.virtual_stub_30"
1078-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1044:13-67
1079            android:exported="false"
1079-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1045:13-37
1080            android:process=":p30" />
1080-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1046:13-35
1081        <provider
1081-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1047:9-1051:38
1082            android:name="com.lody.virtual.client.stub.StubContentProvider$C31"
1082-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1048:13-80
1083            android:authorities="io.virtualapp.virtual_stub_31"
1083-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1049:13-67
1084            android:exported="false"
1084-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1050:13-37
1085            android:process=":p31" />
1085-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1051:13-35
1086        <provider
1086-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1052:9-1056:38
1087            android:name="com.lody.virtual.client.stub.StubContentProvider$C32"
1087-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1053:13-80
1088            android:authorities="io.virtualapp.virtual_stub_32"
1088-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1054:13-67
1089            android:exported="false"
1089-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1055:13-37
1090            android:process=":p32" />
1090-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1056:13-35
1091        <provider
1091-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1057:9-1061:38
1092            android:name="com.lody.virtual.client.stub.StubContentProvider$C33"
1092-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1058:13-80
1093            android:authorities="io.virtualapp.virtual_stub_33"
1093-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1059:13-67
1094            android:exported="false"
1094-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1060:13-37
1095            android:process=":p33" />
1095-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1061:13-35
1096        <provider
1096-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1062:9-1066:38
1097            android:name="com.lody.virtual.client.stub.StubContentProvider$C34"
1097-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1063:13-80
1098            android:authorities="io.virtualapp.virtual_stub_34"
1098-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1064:13-67
1099            android:exported="false"
1099-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1065:13-37
1100            android:process=":p34" />
1100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1066:13-35
1101        <provider
1101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1067:9-1071:38
1102            android:name="com.lody.virtual.client.stub.StubContentProvider$C35"
1102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1068:13-80
1103            android:authorities="io.virtualapp.virtual_stub_35"
1103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1069:13-67
1104            android:exported="false"
1104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1070:13-37
1105            android:process=":p35" />
1105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1071:13-35
1106        <provider
1106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1072:9-1076:38
1107            android:name="com.lody.virtual.client.stub.StubContentProvider$C36"
1107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1073:13-80
1108            android:authorities="io.virtualapp.virtual_stub_36"
1108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1074:13-67
1109            android:exported="false"
1109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1075:13-37
1110            android:process=":p36" />
1110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1076:13-35
1111        <provider
1111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1077:9-1081:38
1112            android:name="com.lody.virtual.client.stub.StubContentProvider$C37"
1112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1078:13-80
1113            android:authorities="io.virtualapp.virtual_stub_37"
1113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1079:13-67
1114            android:exported="false"
1114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1080:13-37
1115            android:process=":p37" />
1115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1081:13-35
1116        <provider
1116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1082:9-1086:38
1117            android:name="com.lody.virtual.client.stub.StubContentProvider$C38"
1117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1083:13-80
1118            android:authorities="io.virtualapp.virtual_stub_38"
1118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1084:13-67
1119            android:exported="false"
1119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1085:13-37
1120            android:process=":p38" />
1120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1086:13-35
1121        <provider
1121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1087:9-1091:38
1122            android:name="com.lody.virtual.client.stub.StubContentProvider$C39"
1122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1088:13-80
1123            android:authorities="io.virtualapp.virtual_stub_39"
1123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1089:13-67
1124            android:exported="false"
1124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1090:13-37
1125            android:process=":p39" />
1125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1091:13-35
1126        <provider
1126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1092:9-1096:38
1127            android:name="com.lody.virtual.client.stub.StubContentProvider$C40"
1127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1093:13-80
1128            android:authorities="io.virtualapp.virtual_stub_40"
1128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1094:13-67
1129            android:exported="false"
1129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1095:13-37
1130            android:process=":p40" />
1130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1096:13-35
1131        <provider
1131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1097:9-1101:38
1132            android:name="com.lody.virtual.client.stub.StubContentProvider$C41"
1132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1098:13-80
1133            android:authorities="io.virtualapp.virtual_stub_41"
1133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1099:13-67
1134            android:exported="false"
1134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1100:13-37
1135            android:process=":p41" />
1135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1101:13-35
1136        <provider
1136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1102:9-1106:38
1137            android:name="com.lody.virtual.client.stub.StubContentProvider$C42"
1137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1103:13-80
1138            android:authorities="io.virtualapp.virtual_stub_42"
1138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1104:13-67
1139            android:exported="false"
1139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1105:13-37
1140            android:process=":p42" />
1140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1106:13-35
1141        <provider
1141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1107:9-1111:38
1142            android:name="com.lody.virtual.client.stub.StubContentProvider$C43"
1142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1108:13-80
1143            android:authorities="io.virtualapp.virtual_stub_43"
1143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1109:13-67
1144            android:exported="false"
1144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1110:13-37
1145            android:process=":p43" />
1145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1111:13-35
1146        <provider
1146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1112:9-1116:38
1147            android:name="com.lody.virtual.client.stub.StubContentProvider$C44"
1147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1113:13-80
1148            android:authorities="io.virtualapp.virtual_stub_44"
1148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1114:13-67
1149            android:exported="false"
1149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1115:13-37
1150            android:process=":p44" />
1150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1116:13-35
1151        <provider
1151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1117:9-1121:38
1152            android:name="com.lody.virtual.client.stub.StubContentProvider$C45"
1152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1118:13-80
1153            android:authorities="io.virtualapp.virtual_stub_45"
1153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1119:13-67
1154            android:exported="false"
1154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1120:13-37
1155            android:process=":p45" />
1155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1121:13-35
1156        <provider
1156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1122:9-1126:38
1157            android:name="com.lody.virtual.client.stub.StubContentProvider$C46"
1157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1123:13-80
1158            android:authorities="io.virtualapp.virtual_stub_46"
1158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1124:13-67
1159            android:exported="false"
1159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1125:13-37
1160            android:process=":p46" />
1160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1126:13-35
1161        <provider
1161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1127:9-1131:38
1162            android:name="com.lody.virtual.client.stub.StubContentProvider$C47"
1162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1128:13-80
1163            android:authorities="io.virtualapp.virtual_stub_47"
1163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1129:13-67
1164            android:exported="false"
1164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1130:13-37
1165            android:process=":p47" />
1165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1131:13-35
1166        <provider
1166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1132:9-1136:38
1167            android:name="com.lody.virtual.client.stub.StubContentProvider$C48"
1167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1133:13-80
1168            android:authorities="io.virtualapp.virtual_stub_48"
1168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1134:13-67
1169            android:exported="false"
1169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1135:13-37
1170            android:process=":p48" />
1170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1136:13-35
1171        <provider
1171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1137:9-1141:38
1172            android:name="com.lody.virtual.client.stub.StubContentProvider$C49"
1172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1138:13-80
1173            android:authorities="io.virtualapp.virtual_stub_49"
1173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1139:13-67
1174            android:exported="false"
1174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1140:13-37
1175            android:process=":p49" />
1175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1141:13-35
1176        <provider
1176-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
1177            android:name="androidx.startup.InitializationProvider"
1177-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
1178            android:authorities="io.virtualapp.androidx-startup"
1178-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
1179            android:exported="false" >
1179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
1180            <meta-data
1180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
1181                android:name="androidx.emoji2.text.EmojiCompatInitializer"
1181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
1182                android:value="androidx.startup" />
1182-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
1183            <meta-data
1183-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
1184                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
1184-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
1185                android:value="androidx.startup" />
1185-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
1186            <meta-data
1186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
1187                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
1187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
1188                android:value="androidx.startup" />
1188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
1189        </provider>
1190
1191        <receiver
1191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
1192            android:name="androidx.profileinstaller.ProfileInstallReceiver"
1192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
1193            android:directBootAware="false"
1193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
1194            android:enabled="true"
1194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
1195            android:exported="true"
1195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
1196            android:permission="android.permission.DUMP" >
1196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
1197            <intent-filter>
1197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
1198                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
1198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
1198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
1199            </intent-filter>
1200            <intent-filter>
1200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
1201                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
1201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
1201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
1202            </intent-filter>
1203            <intent-filter>
1203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
1204                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
1204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
1204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
1205            </intent-filter>
1206            <intent-filter>
1206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
1207                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
1207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
1207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
1208            </intent-filter>
1209        </receiver>
1210    </application>
1211
1212</manifest>
