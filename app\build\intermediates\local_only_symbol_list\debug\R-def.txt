R_DEF: Internal format may change without notice
local
array progress_colors
attr? alphaFactor
attr? card_gap
attr? card_gap_bottom
attr? dsrv_autoScrollEnabled
attr? dsrv_autoScrollHotspotHeight
attr? dsrv_autoScrollHotspot_offsetBottom
attr? dsrv_autoScrollHotspot_offsetTop
attr? ftMaxTextSize
attr? ftMinTextSize
attr? hover
attr? indicatorColor
attr? indicatorName
attr? lv_background_color
attr? lv_fill_triangle
attr? lv_gravity
attr? lv_min_size
attr? lv_padding
attr? lv_text
attr? lv_text_all_caps
attr? lv_text_bold
attr? lv_text_color
attr? lv_text_size
attr? maxHeight
attr? maxWidth
attr? minHeight
attr? minWidth
attr? mrl_rippleAlpha
attr? mrl_rippleBackground
attr? mrl_rippleColor
attr? mrl_rippleDelayClick
attr? mrl_rippleDimension
attr? mrl_rippleDuration
attr? mrl_rippleFadeDuration
attr? mrl_rippleHover
attr? mrl_rippleInAdapter
attr? mrl_rippleOverlay
attr? mrl_ripplePersistent
attr? mrl_rippleRoundedCorners
attr? parallax_enabled
attr? parallax_scale
attr? pi_force_square
attr? pi_mask_color
attr? pi_progress
attr? pi_radius
attr? pi_stroke
attr? reflectionColor
attr? rippleColor
attr? showInitAnimation
color black_20_transparent
color colorAccent
color colorPrimary
color colorPrimaryDark
color colorPrimaryRavel
color desktopColorA
color desktopColorB
color desktopColorC
color desktopColorD
color holo_blue_dark
color holo_green_dark
color holo_purple_dark
color holo_red_dark
color holo_yellow_dark
color mainTextColor
color md_transparent
color transparent
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen card_gap
dimen card_gap_bottom
dimen card_radius
dimen desktop_divider
dimen dp10
dimen dp16
dimen dp24
dimen dp30
dimen dp8
dimen dp80
dimen dsrv_defaultHotspotHeight
dimen item_height
dimen line_height
drawable blue_circle
drawable fab_bg
drawable home_bg
drawable ic_about
drawable ic_account
drawable ic_add
drawable ic_add_circle
drawable ic_check
drawable ic_crash
drawable ic_device
drawable ic_menu
drawable ic_no_check
drawable ic_notification
drawable ic_settings
drawable ic_shortcut
drawable ic_vs
drawable ic_wifi
drawable icon_bg
drawable sel_clone_app_btn
drawable sel_guide_btn
drawable shape_clone_app_btn
drawable shape_clone_app_btn_pressed
id BOTTOM_LEFT
id BOTTOM_RIGHT
id TOP_LEFT
id TOP_RIGHT
id action_clear
id action_ok
id action_refresh
id address
id app_icon
id app_name
id appdata_list
id bottom_area
id cardstack_internal_position_tag
id clone_app_tab_layout
id clone_app_tool_bar
id clone_app_view_pager
id create_shortcut_area
id create_shortcut_text
id delete_app_area
id delete_app_text
id home_launcher
id home_menu
id item_app_checked
id item_app_clone_count
id item_app_icon
id item_app_name
id item_app_space_idx
id item_first_open_dot
id item_location
id iv_icon
id loading_anim
id map
id pb_loading_app
id select_app_install_btn
id select_app_progress_bar
id select_app_recycler_view
id task_top_toolbar
id textView
id tv_title
id user_list
integer parallax_scale_default
layout activity_clone_app
layout activity_home
layout activity_install
layout activity_loading
layout activity_location_settings
layout activity_marker
layout activity_splash
layout activity_users
layout content_toolbar
layout fragment_list_app
layout item_app
layout item_clone_app
layout item_launcher_app
layout item_location_app
layout item_user
menu marktet_map
menu user_menu
mipmap ic_launcher
string about
string add_app
string app_name
string brand
string clone_apps
string config_device_info
string create_shortcut
string delete
string desktop
string device
string enable
string external_storage
string fake_device_info
string install_d
string install_too_much_once_time
string manufacturer
string new_user
string preparing
string save
string save_success
string wait
string wifi_status
style AVLoadingIndicatorView
style AppTheme
style AppTheme.AppBarOverlay
style AppTheme.PopupOverlay
style TransparentTheme
style UITheme
style VAAlertTheme
styleable CardStackLayout parallax_enabled parallax_scale card_gap card_gap_bottom showInitAnimation
styleable DragSelectRecyclerView dsrv_autoScrollHotspotHeight dsrv_autoScrollEnabled dsrv_autoScrollHotspot_offsetTop dsrv_autoScrollHotspot_offsetBottom
styleable FitTextView ftMinTextSize ftMaxTextSize
styleable LabelView lv_text lv_text_color lv_text_size lv_text_bold lv_text_all_caps lv_background_color lv_min_size lv_padding lv_gravity lv_fill_triangle
styleable LoadingIndicatorView minWidth maxWidth minHeight maxHeight indicatorName indicatorColor
styleable MaterialRippleLayout mrl_rippleColor mrl_rippleDimension mrl_rippleOverlay mrl_rippleAlpha mrl_rippleDuration mrl_rippleFadeDuration mrl_rippleHover mrl_rippleBackground mrl_rippleDelayClick mrl_ripplePersistent mrl_rippleInAdapter mrl_rippleRoundedCorners
styleable ProgressImageView pi_progress pi_mask_color pi_stroke pi_radius pi_force_square
styleable RippleButton rippleColor alphaFactor hover
styleable ShimmerView reflectionColor
