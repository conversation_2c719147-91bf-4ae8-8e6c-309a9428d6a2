<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="UITheme" parent="Theme.AppCompat.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="TransparentTheme" parent="AppTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@android:color/background_dark</item>
        <item name="colorPrimaryDark">@android:color/background_dark</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="VAAlertTheme" parent="Theme.AppCompat.Light.Dialog.Alert" />

    <style name="AVLoadingIndicatorView">
        <item name="minWidth">48dip</item>
        <item name="maxWidth">48dip</item>
        <item name="minHeight">48dip</item>
        <item name="maxHeight">48dip</item>
        <item name="indicatorName">BallPulseIndicator</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>

</resources>
