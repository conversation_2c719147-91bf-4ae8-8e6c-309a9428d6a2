package com.lody.virtual.helper.compat;

/**
 * <AUTHOR>
 */

public class ContentResolverCompat {

    public static final int SYNC_OBSERVER_TYPE_STATUS = 1 << 3;

    
    public static final int SYNC_ERROR_SYNC_ALREADY_IN_PROGRESS = 1;
    
    public static final int SYNC_ERROR_AUTHENTICATION = 2;
    
    public static final int SYNC_ERROR_IO = 3;
    
    public static final int SYNC_ERROR_PARSE = 4;
    
    public static final int SYNC_ERROR_CONFLICT = 5;
    
    public static final int SYNC_ERROR_TOO_MANY_DELETIONS = 6;
    
    public static final int SYNC_ERROR_TOO_MANY_RETRIES = 7;
    
    public static final int SYNC_ERROR_INTERNAL = 8;
}
