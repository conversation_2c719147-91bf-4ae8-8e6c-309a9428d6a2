/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.lody.virtual.client;
public interface IVClient extends android.os.IInterface
{
  /** Default implementation for IVClient. */
  public static class Default implements com.lody.virtual.client.IVClient
  {
    @Override public void scheduleReceiver(java.lang.String processName, android.content.ComponentName component, android.content.Intent intent, com.lody.virtual.remote.PendingResultData resultData) throws android.os.RemoteException
    {
    }
    @Override public void scheduleNewIntent(java.lang.String creator, android.os.IBinder token, android.content.Intent intent) throws android.os.RemoteException
    {
    }
    @Override public void finishActivity(android.os.IBinder token) throws android.os.RemoteException
    {
    }
    @Override public android.os.IBinder createProxyService(android.content.ComponentName component, android.os.IBinder binder) throws android.os.RemoteException
    {
      return null;
    }
    @Override public android.os.IBinder acquireProviderClient(android.content.pm.ProviderInfo info) throws android.os.RemoteException
    {
      return null;
    }
    @Override public android.os.IBinder getAppThread() throws android.os.RemoteException
    {
      return null;
    }
    @Override public android.os.IBinder getToken() throws android.os.RemoteException
    {
      return null;
    }
    @Override public java.lang.String getDebugInfo() throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.lody.virtual.client.IVClient
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.lody.virtual.client.IVClient interface,
     * generating a proxy if needed.
     */
    public static com.lody.virtual.client.IVClient asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.lody.virtual.client.IVClient))) {
        return ((com.lody.virtual.client.IVClient)iin);
      }
      return new com.lody.virtual.client.IVClient.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_scheduleReceiver:
        {
          java.lang.String _arg0;
          _arg0 = data.readString();
          android.content.ComponentName _arg1;
          _arg1 = _Parcel.readTypedObject(data, android.content.ComponentName.CREATOR);
          android.content.Intent _arg2;
          _arg2 = _Parcel.readTypedObject(data, android.content.Intent.CREATOR);
          com.lody.virtual.remote.PendingResultData _arg3;
          _arg3 = _Parcel.readTypedObject(data, com.lody.virtual.remote.PendingResultData.CREATOR);
          this.scheduleReceiver(_arg0, _arg1, _arg2, _arg3);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_scheduleNewIntent:
        {
          java.lang.String _arg0;
          _arg0 = data.readString();
          android.os.IBinder _arg1;
          _arg1 = data.readStrongBinder();
          android.content.Intent _arg2;
          _arg2 = _Parcel.readTypedObject(data, android.content.Intent.CREATOR);
          this.scheduleNewIntent(_arg0, _arg1, _arg2);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_finishActivity:
        {
          android.os.IBinder _arg0;
          _arg0 = data.readStrongBinder();
          this.finishActivity(_arg0);
          reply.writeNoException();
          break;
        }
        case TRANSACTION_createProxyService:
        {
          android.content.ComponentName _arg0;
          _arg0 = _Parcel.readTypedObject(data, android.content.ComponentName.CREATOR);
          android.os.IBinder _arg1;
          _arg1 = data.readStrongBinder();
          android.os.IBinder _result = this.createProxyService(_arg0, _arg1);
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          break;
        }
        case TRANSACTION_acquireProviderClient:
        {
          android.content.pm.ProviderInfo _arg0;
          _arg0 = _Parcel.readTypedObject(data, android.content.pm.ProviderInfo.CREATOR);
          android.os.IBinder _result = this.acquireProviderClient(_arg0);
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          break;
        }
        case TRANSACTION_getAppThread:
        {
          android.os.IBinder _result = this.getAppThread();
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          break;
        }
        case TRANSACTION_getToken:
        {
          android.os.IBinder _result = this.getToken();
          reply.writeNoException();
          reply.writeStrongBinder(_result);
          break;
        }
        case TRANSACTION_getDebugInfo:
        {
          java.lang.String _result = this.getDebugInfo();
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements com.lody.virtual.client.IVClient
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void scheduleReceiver(java.lang.String processName, android.content.ComponentName component, android.content.Intent intent, com.lody.virtual.remote.PendingResultData resultData) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(processName);
          _Parcel.writeTypedObject(_data, component, 0);
          _Parcel.writeTypedObject(_data, intent, 0);
          _Parcel.writeTypedObject(_data, resultData, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_scheduleReceiver, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void scheduleNewIntent(java.lang.String creator, android.os.IBinder token, android.content.Intent intent) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(creator);
          _data.writeStrongBinder(token);
          _Parcel.writeTypedObject(_data, intent, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_scheduleNewIntent, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public void finishActivity(android.os.IBinder token) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeStrongBinder(token);
          boolean _status = mRemote.transact(Stub.TRANSACTION_finishActivity, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      @Override public android.os.IBinder createProxyService(android.content.ComponentName component, android.os.IBinder binder) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _Parcel.writeTypedObject(_data, component, 0);
          _data.writeStrongBinder(binder);
          boolean _status = mRemote.transact(Stub.TRANSACTION_createProxyService, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public android.os.IBinder acquireProviderClient(android.content.pm.ProviderInfo info) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _Parcel.writeTypedObject(_data, info, 0);
          boolean _status = mRemote.transact(Stub.TRANSACTION_acquireProviderClient, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public android.os.IBinder getAppThread() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getAppThread, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public android.os.IBinder getToken() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        android.os.IBinder _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getToken, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readStrongBinder();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public java.lang.String getDebugInfo() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getDebugInfo, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
    }
    static final int TRANSACTION_scheduleReceiver = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_scheduleNewIntent = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_finishActivity = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_createProxyService = (android.os.IBinder.FIRST_CALL_TRANSACTION + 3);
    static final int TRANSACTION_acquireProviderClient = (android.os.IBinder.FIRST_CALL_TRANSACTION + 4);
    static final int TRANSACTION_getAppThread = (android.os.IBinder.FIRST_CALL_TRANSACTION + 5);
    static final int TRANSACTION_getToken = (android.os.IBinder.FIRST_CALL_TRANSACTION + 6);
    static final int TRANSACTION_getDebugInfo = (android.os.IBinder.FIRST_CALL_TRANSACTION + 7);
  }
  public static final java.lang.String DESCRIPTOR = "com.lody.virtual.client.IVClient";
  public void scheduleReceiver(java.lang.String processName, android.content.ComponentName component, android.content.Intent intent, com.lody.virtual.remote.PendingResultData resultData) throws android.os.RemoteException;
  public void scheduleNewIntent(java.lang.String creator, android.os.IBinder token, android.content.Intent intent) throws android.os.RemoteException;
  public void finishActivity(android.os.IBinder token) throws android.os.RemoteException;
  public android.os.IBinder createProxyService(android.content.ComponentName component, android.os.IBinder binder) throws android.os.RemoteException;
  public android.os.IBinder acquireProviderClient(android.content.pm.ProviderInfo info) throws android.os.RemoteException;
  public android.os.IBinder getAppThread() throws android.os.RemoteException;
  public android.os.IBinder getToken() throws android.os.RemoteException;
  public java.lang.String getDebugInfo() throws android.os.RemoteException;
  /** @hide */
  static class _Parcel {
    static private <T> T readTypedObject(
        android.os.Parcel parcel,
        android.os.Parcelable.Creator<T> c) {
      if (parcel.readInt() != 0) {
          return c.createFromParcel(parcel);
      } else {
          return null;
      }
    }
    static private <T extends android.os.Parcelable> void writeTypedObject(
        android.os.Parcel parcel, T value, int parcelableFlags) {
      if (value != null) {
        parcel.writeInt(1);
        value.writeToParcel(parcel, parcelableFlags);
      } else {
        parcel.writeInt(0);
      }
    }
  }
}
