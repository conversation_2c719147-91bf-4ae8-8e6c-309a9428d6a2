1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.lody.virtual" >
5
6    <uses-sdk android:minSdkVersion="21" />
7
8    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
8-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:5:5-88
8-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:5:22-85
9    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
9-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:7:5-77
9-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:7:22-74
10    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
10-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:8:5-78
10-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:8:22-75
11    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:9:5-78
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:9:22-75
12    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:10:5-80
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:10:22-77
13    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
13-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:11:5-81
13-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:11:22-78
14    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
14-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:12:5-95
14-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:12:22-92
15    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
15-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:13:5-117
15-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:13:22-114
16    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
16-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:14:5-106
16-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:14:22-103
17    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
17-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:15:5-107
17-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:15:22-104
18    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
18-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:16:5-86
18-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:16:22-83
19    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
19-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:17:5-76
19-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:17:22-73
20    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
20-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:18:5-82
20-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:18:22-79
21    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" />
21-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:19:5-83
21-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:19:22-80
22    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
22-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:21:5-80
22-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:21:22-77
23    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
23-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:22:5-79
23-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:22:22-76
24    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
24-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:23:5-97
24-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:23:22-94
25    <uses-permission
25-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:25:5-27:47
26        android:name="android.permission.ACCOUNT_MANAGER"
26-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:26:9-58
27        tools:ignore="ProtectedPermissions" />
27-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:27:9-44
28    <uses-permission
28-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:29:5-31:47
29        android:name="android.permission.PACKAGE_USAGE_STATS"
29-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:30:9-62
30        tools:ignore="ProtectedPermissions" />
30-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:31:9-44
31    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
31-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:32:5-74
31-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:32:22-71
32    <uses-permission android:name="android.permission.INTERNET" />
32-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:33:5-67
32-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:33:22-64
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
33-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:34:5-81
33-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:34:22-78
34    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
34-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:35:5-79
34-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
35-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:36:5-89
35-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:36:22-86
36    <uses-permission
36-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:37:5-39:39
37        android:name="android.permission.ACCESS_MOCK_LOCATION"
37-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:38:9-63
38        tools:ignore="MockLocation" />
38-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:39:9-36
39    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
39-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:40:5-79
39-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:40:22-76
40    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
40-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:41:5-76
40-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:41:22-73
41    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
41-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:42:5-77
41-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:42:22-74
42    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
42-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:43:5-80
42-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:43:22-77
43    <uses-permission
43-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:44:5-46:47
44        android:name="android.permission.BIND_APPWIDGET"
44-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:45:9-57
45        tools:ignore="ProtectedPermissions" />
45-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:46:9-44
46    <uses-permission android:name="android.permission.BLUETOOTH" />
46-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:47:5-68
46-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:47:22-65
47    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
47-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:48:5-74
47-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:48:22-71
48    <uses-permission android:name="android.permission.BODY_SENSORS" />
48-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:49:5-71
48-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:49:22-68
49    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
49-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:50:5-75
49-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:50:22-72
50    <uses-permission android:name="android.permission.CALL_PHONE" />
50-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:51:5-69
50-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:51:22-66
51    <uses-permission android:name="android.permission.CAMERA" />
51-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:52:5-65
51-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:52:22-62
52    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
52-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:53:5-79
52-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:53:22-76
53    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
53-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:54:5-86
53-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:54:22-83
54    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
54-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:55:5-76
54-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:55:22-73
55    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
55-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:56:5-77
55-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:56:22-74
56    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
56-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:57:5-74
56-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:57:22-71
57    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
57-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:58:5-75
57-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:58:22-72
58    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
58-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:59:5-88
58-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:59:22-85
59    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
59-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:60:5-76
59-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:60:22-73
60    <uses-permission android:name="android.permission.FLASHLIGHT" />
60-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:61:5-69
60-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:61:22-66
61    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
61-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:62:5-71
61-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:62:22-68
62    <uses-permission android:name="android.permission.GET_CLIPS" />
62-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:63:5-68
62-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:63:22-65
63    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
63-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:64:5-75
63-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:64:22-72
64    <uses-permission android:name="android.permission.GET_TASKS" />
64-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:65:5-68
64-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:65:22-65
65    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
65-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:66:5-84
65-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:66:22-81
66    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
66-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:67:5-74
66-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:67:22-71
67    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
67-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:68:5-80
67-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:68:22-77
68    <uses-permission android:name="android.permission.NFC" />
68-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:69:5-62
68-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:69:22-59
69    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
69-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:70:5-78
69-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:70:22-75
70    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
70-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:71:5-81
70-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:71:22-78
71    <uses-permission android:name="android.permission.READ_CALENDAR" />
71-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:72:5-72
71-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:72:22-69
72    <uses-permission android:name="android.permission.READ_CALL_LOG" />
72-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:73:5-72
72-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:73:22-69
73    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
73-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:74:5-79
73-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:74:22-76
74    <uses-permission android:name="android.permission.READ_CLIPS" />
74-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:75:5-69
74-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:75:22-66
75    <uses-permission android:name="android.permission.READ_CONTACTS" />
75-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:76:5-72
75-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:76:22-69
76    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
76-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:77:5-80
76-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:77:22-77
77    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
77-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:78:5-80
77-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:78:22-77
78    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
78-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:79:5-75
78-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:79:22-72
79    <uses-permission android:name="android.permission.READ_PROFILE" />
79-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:80:5-71
79-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:80:22-68
80    <uses-permission android:name="android.permission.READ_SMS" />
80-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:81:5-67
80-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:81:22-64
81    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
81-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:82:5-77
81-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:82:22-74
82    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
82-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:83:5-77
82-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:83:22-74
83    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
83-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:84:5-74
83-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:84:22-71
84    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
84-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:85:5-79
84-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:85:22-76
85    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
85-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:86:5-81
85-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:86:22-78
86    <uses-permission android:name="android.permission.RECEIVE_MMS" />
86-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:87:5-70
86-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:87:22-67
87    <uses-permission android:name="android.permission.RECEIVE_SMS" />
87-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:88:5-70
87-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:88:22-67
88    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
88-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:89:5-75
88-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:89:22-72
89    <uses-permission android:name="android.permission.RECORD_AUDIO" />
89-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:90:5-71
89-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:90:22-68
90    <uses-permission android:name="android.permission.REORDER_TASKS" />
90-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:91:5-72
90-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:91:22-69
91    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
91-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:92:5-75
91-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:92:22-72
92    <uses-permission android:name="android.permission.SEND_SMS" />
92-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:93:5-67
92-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:93:22-64
93    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
93-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:94:5-72
93-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:94:22-69
94    <uses-permission android:name="android.permission.SET_WALLPAPER" />
94-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:95:5-72
94-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:95:22-69
95    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
95-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:96:5-78
95-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:96:22-75
96    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
96-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:97:5-80
96-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:97:22-77
97    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
97-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:98:5-81
97-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:98:22-78
98    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
98-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:99:5-78
98-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:99:22-75
99    <uses-permission android:name="android.permission.TRANSMIT_IR" />
99-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:100:5-70
99-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:100:22-67
100    <uses-permission android:name="android.permission.USE_SIP" />
100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:101:5-66
100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:101:22-63
101    <uses-permission android:name="android.permission.VIBRATE" />
101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:102:5-66
101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:102:22-63
102    <uses-permission android:name="android.permission.WAKE_LOCK" />
102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:103:5-68
102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:103:22-65
103    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:104:5-73
103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:104:22-70
104    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:105:5-73
104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:105:22-70
105    <uses-permission android:name="android.permission.WRITE_CLIPS" />
105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:106:5-70
105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:106:22-67
106    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:107:5-73
106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:107:22-70
107    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:108:5-81
107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:108:22-78
108    <uses-permission android:name="android.permission.WRITE_PROFILE" />
108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:109:5-72
108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:109:22-69
109    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:110:5-73
109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:110:22-70
110    <uses-permission android:name="android.permission.WRITE_SMS" />
110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:111:5-68
110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:111:22-65
111    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:112:5-78
111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:112:22-75
112    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:113:5-78
112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:113:22-75
113    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:114:5-80
113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:114:22-77
114    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:115:5-74
114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:115:22-71
115    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:116:5-78
115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:116:22-75
116    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:117:5-93
116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:117:22-90
117    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:118:5-94
117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:118:22-91
118    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:119:5-88
118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:119:22-85
119    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:120:5-90
119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:120:22-87
120    <uses-permission android:name="com.android.vending.BILLING" />
120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:121:5-67
120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:121:22-64
121    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:122:5-73
121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:122:22-70
122    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:123:5-86
122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:123:22-83
123    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:124:5-82
123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:124:22-79
124    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:125:5-94
124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:125:22-91
125    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:126:5-92
125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:126:22-89
126    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:127:5-92
126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:127:22-89
127    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:128:5-107
127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:128:22-104
128    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:129:5-104
128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:129:22-101
129    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adsense" />
129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:130:5-100
129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:130:22-97
130    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adwords" />
130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:131:5-100
130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:131:22-97
131    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ah" />
131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:132:5-95
131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:132:22-92
132    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.android" />
132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:133:5-100
132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:133:22-97
133    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.androidsecure" />
133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:134:5-106
133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:134:22-103
134    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.blogger" />
134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:135:5-100
134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:135:22-97
135    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cl" />
135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:136:5-95
135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:136:22-92
136    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cp" />
136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:137:5-95
136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:137:22-92
137    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.dodgeball" />
137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:138:5-102
137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:138:22-99
138    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.finance" />
138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:139:5-100
138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:139:22-97
139    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.gbase" />
139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:140:5-98
139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:140:22-95
140    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.grandcentral" />
140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:141:5-105
140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:141:22-102
141    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.groups2" />
141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:142:5-100
141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:142:22-97
142    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.health" />
142-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:143:5-99
142-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:143:22-96
143    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ig" />
143-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:144:5-95
143-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:144:22-92
144    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.jotspot" />
144-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:145:5-100
144-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:145:22-97
145    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.knol" />
145-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:146:5-97
145-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:146:22-94
146    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.lh2" />
146-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:147:5-96
146-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:147:22-93
147    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.local" />
147-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:148:5-98
147-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:148:22-95
148    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mail" />
148-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:149:5-97
148-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:149:22-94
149    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mobile" />
149-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:150:5-99
149-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:150:22-96
150    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.news" />
150-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:151:5-97
150-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:151:22-94
151    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.notebook" />
151-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:152:5-101
151-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:152:22-98
152    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.orkut" />
152-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:153:5-98
152-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:153:22-95
153    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.print" />
153-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:154:5-98
153-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:154:22-95
154    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierra" />
154-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:155:5-99
154-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:155:22-96
155    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierraqa" />
155-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:156:5-101
155-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:156:22-98
156    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierrasandbox" />
156-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:157:5-106
156-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:157:22-103
157    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sitemaps" />
157-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:158:5-101
157-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:158:22-98
158    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speech" />
158-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:159:5-99
158-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:159:22-96
159    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speechpersonalization" />
159-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:160:5-114
159-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:160:22-111
160    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.talk" />
160-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:161:5-97
160-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:161:22-94
161    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wifi" />
161-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:162:5-97
161-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:162:22-94
162    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wise" />
162-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:163:5-97
162-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:163:22-94
163    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.writely" />
163-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:164:5-100
163-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:164:22-97
164    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.youtube" />
164-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:165:5-100
164-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:165:22-97
165    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
165-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:166:5-92
165-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:166:22-89
166    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
166-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:167:5-98
166-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:167:22-95
167    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
167-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:168:5-94
167-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:168:22-91
168    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
168-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:169:5-95
168-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:169:22-92
169    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
169-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:170:5-84
169-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:170:22-81
170    <uses-permission android:name="android.permission.READ_LOGS" />
170-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:171:5-68
170-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:171:22-65
171    <uses-permission
171-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:172:5-174:47
172        android:name="android.permission.INSTALL_PACKAGES"
172-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:173:9-59
173        tools:ignore="ProtectedPermissions" />
173-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:174:9-44
174    <uses-permission
174-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:175:5-177:47
175        android:name="android.permission.DELETE_PACKAGES"
175-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:176:9-58
176        tools:ignore="ProtectedPermissions" />
176-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:177:9-44
177    <uses-permission
177-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:178:5-180:47
178        android:name="android.permission.CLEAR_APP_USER_DATA"
178-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:179:9-62
179        tools:ignore="ProtectedPermissions" />
179-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:180:9-44
180    <uses-permission
180-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:181:5-183:47
181        android:name="android.permission.WRITE_MEDIA_STORAGE"
181-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:182:9-62
182        tools:ignore="ProtectedPermissions" />
182-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:183:9-44
183    <uses-permission
183-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:184:5-186:47
184        android:name="android.permission.ACCESS_CACHE_FILESYSTEM"
184-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:185:9-66
185        tools:ignore="ProtectedPermissions" />
185-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:186:9-44
186    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
186-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:187:5-74
186-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:187:22-71
187    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
187-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:188:5-75
187-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:188:22-72
188    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
188-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:189:5-79
188-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:189:22-76
189    <uses-permission
189-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:190:5-192:47
190        android:name="android.permission.DEVICE_POWER"
190-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:191:9-55
191        tools:ignore="ProtectedPermissions" />
191-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:192:9-44
192    <uses-permission android:name="android.permission.BATTERY_STATS" />
192-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:193:5-72
192-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:193:22-69
193    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
193-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:194:5-82
193-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:194:22-79
194    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
194-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:195:5-85
194-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:195:22-82
195    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
195-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:196:5-86
195-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:196:22-83
196    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
196-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:197:5-86
196-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:197:22-83
197    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
197-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:198:5-86
197-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:198:22-83
198    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
198-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:199:5-89
198-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:199:22-86
199    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
199-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:200:5-93
199-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:200:22-90
200    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
200-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:201:5-80
200-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:201:22-77
201    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
201-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:202:5-85
201-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:202:22-82
202    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
202-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:203:5-86
202-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:203:22-83
203    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
203-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:204:5-78
203-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:204:22-75
204    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
204-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:205:5-81
204-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:205:22-78
205    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
205-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:206:5-84
205-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:206:22-81
206    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
206-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:207:5-82
206-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:207:22-79
207    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
207-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:208:5-82
207-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:208:22-79
208    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
208-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:209:5-79
208-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:209:22-76
209    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
209-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:210:5-82
209-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:210:22-79
210    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
210-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:211:5-92
210-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:211:22-89
211    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:212:5-91
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:212:22-88
212    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
212-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:213:5-83
212-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:213:22-80
213    <uses-permission
213-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:215:5-217:47
214        android:name="android.permission.WRITE_APN_SETTINGS"
214-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:216:9-61
215        tools:ignore="ProtectedPermissions" />
215-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:217:9-44
216
217    <uses-feature android:name="android.hardware.camera" />
217-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:219:5-60
217-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:219:19-57
218    <uses-feature
218-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:220:5-222:36
219        android:name="android.hardware.camera.autofocus"
219-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:221:9-57
220        android:required="false" />
220-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:222:9-33
221
222    <application>
222-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:224:5-1298:19
223        <service
223-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:225:9-227:61
224            android:name="com.lody.virtual.client.stub.DaemonService"
224-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:226:13-70
225            android:process="@string/engine_process_name" />
225-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:227:13-58
226        <service
226-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:229:9-231:61
227            android:name="com.lody.virtual.client.stub.DaemonService$InnerService"
227-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:230:13-83
228            android:process="@string/engine_process_name" />
228-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:231:13-58
229
230        <activity
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:233:9-237:75
231            android:name="com.lody.virtual.client.stub.ShortcutHandleActivity"
231-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:234:13-79
232            android:exported="true"
232-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:235:13-36
233            android:process="@string/engine_process_name"
233-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:236:13-58
234            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
234-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:237:13-72
235        <activity
235-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:239:9-241:61
236            android:name="com.lody.virtual.client.stub.StubPendingActivity"
236-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:240:13-60
237            android:process="@string/engine_process_name" />
237-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:241:13-58
238
239        <service
239-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:243:9-245:61
240            android:name="com.lody.virtual.client.stub.StubPendingService"
240-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:244:13-59
241            android:process="@string/engine_process_name" />
241-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:245:13-58
242
243        <receiver
243-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:246:9-248:61
244            android:name="com.lody.virtual.client.stub.StubPendingReceiver"
244-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:247:13-60
245            android:process="@string/engine_process_name" />
245-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:248:13-58
246
247        <service
247-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:250:9-253:61
248            android:name="com.lody.virtual.client.stub.StubJob"
248-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:251:13-48
249            android:permission="android.permission.BIND_JOB_SERVICE"
249-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:252:13-69
250            android:process="@string/engine_process_name" />
250-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:253:13-58
251
252        <activity
252-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:255:9-261:52
253            android:name="com.lody.virtual.client.stub.ChooseAccountTypeActivity"
253-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:256:13-66
254            android:configChanges="keyboard|keyboardHidden|orientation"
254-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:257:13-72
255            android:excludeFromRecents="true"
255-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:258:13-46
256            android:exported="false"
256-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:259:13-37
257            android:process="@string/engine_process_name"
257-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:260:13-58
258            android:screenOrientation="portrait" />
258-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:261:13-49
259        <activity
259-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:263:9-269:52
260            android:name="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"
260-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:264:13-69
261            android:configChanges="keyboard|keyboardHidden|orientation"
261-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:265:13-72
262            android:excludeFromRecents="true"
262-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:266:13-46
263            android:exported="false"
263-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:267:13-37
264            android:process="@string/engine_process_name"
264-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:268:13-58
265            android:screenOrientation="portrait" />
265-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:269:13-49
266        <activity
266-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:271:9-279:51
267            android:name="com.lody.virtual.client.stub.ChooserActivity"
267-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:272:13-56
268            android:configChanges="keyboard|keyboardHidden|orientation"
268-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:273:13-72
269            android:excludeFromRecents="true"
269-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:274:13-46
270            android:exported="true"
270-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:275:13-36
271            android:finishOnCloseSystemDialogs="true"
271-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:276:13-54
272            android:process="@string/engine_process_name"
272-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:277:13-58
273            android:screenOrientation="portrait"
273-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:278:13-49
274            android:theme="@style/VAAlertTheme" />
274-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:279:13-48
275        <activity
275-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:281:9-289:51
276            android:name="com.lody.virtual.client.stub.ResolverActivity"
276-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:282:13-57
277            android:configChanges="keyboard|keyboardHidden|orientation"
277-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:283:13-72
278            android:excludeFromRecents="true"
278-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:284:13-46
279            android:exported="true"
279-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:285:13-36
280            android:finishOnCloseSystemDialogs="true"
280-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:286:13-54
281            android:process="@string/engine_process_name"
281-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:287:13-58
282            android:screenOrientation="portrait"
282-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:288:13-49
283            android:theme="@style/VAAlertTheme" />
283-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:289:13-48
284
285        <provider
285-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:291:9-295:61
286            android:name="com.lody.virtual.server.BinderProvider"
286-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:292:13-66
287            android:authorities="${applicationId}.virtual.service.BinderProvider"
287-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:293:13-82
288            android:exported="false"
288-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:294:13-37
289            android:process="@string/engine_process_name" />
289-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:295:13-58
290
291        <activity
291-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:297:9-302:46
292            android:name="com.lody.virtual.client.stub.StubActivity$C0"
292-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:298:13-72
293            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
293-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:299:13-170
294            android:process=":p0"
294-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:300:13-34
295            android:taskAffinity="com.lody.virtual.vt"
295-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:301:13-55
296            android:theme="@style/VATheme" />
296-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:302:13-43
297        <activity
297-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:304:9-309:46
298            android:name="com.lody.virtual.client.stub.StubActivity$C1"
298-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:305:13-72
299            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
299-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:306:13-170
300            android:process=":p1"
300-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:307:13-34
301            android:taskAffinity="com.lody.virtual.vt"
301-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:308:13-55
302            android:theme="@style/VATheme" />
302-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:309:13-43
303        <activity
303-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:311:9-316:46
304            android:name="com.lody.virtual.client.stub.StubActivity$C2"
304-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:312:13-72
305            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
305-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:313:13-170
306            android:process=":p2"
306-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:314:13-34
307            android:taskAffinity="com.lody.virtual.vt"
307-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:315:13-55
308            android:theme="@style/VATheme" />
308-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:316:13-43
309        <activity
309-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:318:9-323:46
310            android:name="com.lody.virtual.client.stub.StubActivity$C3"
310-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:319:13-72
311            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
311-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:320:13-170
312            android:process=":p3"
312-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:321:13-34
313            android:taskAffinity="com.lody.virtual.vt"
313-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:322:13-55
314            android:theme="@style/VATheme" />
314-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:323:13-43
315        <activity
315-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:325:9-330:46
316            android:name="com.lody.virtual.client.stub.StubActivity$C4"
316-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:326:13-72
317            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
317-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:327:13-170
318            android:process=":p4"
318-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:328:13-34
319            android:taskAffinity="com.lody.virtual.vt"
319-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:329:13-55
320            android:theme="@style/VATheme" />
320-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:330:13-43
321        <activity
321-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:332:9-337:46
322            android:name="com.lody.virtual.client.stub.StubActivity$C5"
322-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:333:13-72
323            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
323-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:334:13-170
324            android:process=":p5"
324-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:335:13-34
325            android:taskAffinity="com.lody.virtual.vt"
325-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:336:13-55
326            android:theme="@style/VATheme" />
326-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:337:13-43
327        <activity
327-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:339:9-344:46
328            android:name="com.lody.virtual.client.stub.StubActivity$C6"
328-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:340:13-72
329            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
329-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:341:13-170
330            android:process=":p6"
330-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:342:13-34
331            android:taskAffinity="com.lody.virtual.vt"
331-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:343:13-55
332            android:theme="@style/VATheme" />
332-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:344:13-43
333        <activity
333-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:346:9-351:46
334            android:name="com.lody.virtual.client.stub.StubActivity$C7"
334-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:347:13-72
335            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
335-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:348:13-170
336            android:process=":p7"
336-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:349:13-34
337            android:taskAffinity="com.lody.virtual.vt"
337-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:350:13-55
338            android:theme="@style/VATheme" />
338-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:351:13-43
339        <activity
339-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:353:9-358:46
340            android:name="com.lody.virtual.client.stub.StubActivity$C8"
340-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:354:13-72
341            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
341-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:355:13-170
342            android:process=":p8"
342-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:356:13-34
343            android:taskAffinity="com.lody.virtual.vt"
343-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:357:13-55
344            android:theme="@style/VATheme" />
344-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:358:13-43
345        <activity
345-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:360:9-365:46
346            android:name="com.lody.virtual.client.stub.StubActivity$C9"
346-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:361:13-72
347            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
347-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:362:13-170
348            android:process=":p9"
348-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:363:13-34
349            android:taskAffinity="com.lody.virtual.vt"
349-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:364:13-55
350            android:theme="@style/VATheme" />
350-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:365:13-43
351        <activity
351-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:367:9-372:46
352            android:name="com.lody.virtual.client.stub.StubActivity$C10"
352-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:368:13-73
353            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
353-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:369:13-170
354            android:process=":p10"
354-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:370:13-35
355            android:taskAffinity="com.lody.virtual.vt"
355-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:371:13-55
356            android:theme="@style/VATheme" />
356-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:372:13-43
357        <activity
357-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:374:9-379:46
358            android:name="com.lody.virtual.client.stub.StubActivity$C11"
358-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:375:13-73
359            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
359-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:376:13-170
360            android:process=":p11"
360-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:377:13-35
361            android:taskAffinity="com.lody.virtual.vt"
361-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:378:13-55
362            android:theme="@style/VATheme" />
362-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:379:13-43
363        <activity
363-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:381:9-386:46
364            android:name="com.lody.virtual.client.stub.StubActivity$C12"
364-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:382:13-73
365            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
365-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:383:13-170
366            android:process=":p12"
366-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:384:13-35
367            android:taskAffinity="com.lody.virtual.vt"
367-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:385:13-55
368            android:theme="@style/VATheme" />
368-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:386:13-43
369        <activity
369-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:388:9-393:46
370            android:name="com.lody.virtual.client.stub.StubActivity$C13"
370-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:389:13-73
371            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
371-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:390:13-170
372            android:process=":p13"
372-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:391:13-35
373            android:taskAffinity="com.lody.virtual.vt"
373-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:392:13-55
374            android:theme="@style/VATheme" />
374-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:393:13-43
375        <activity
375-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:395:9-400:46
376            android:name="com.lody.virtual.client.stub.StubActivity$C14"
376-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:396:13-73
377            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
377-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:397:13-170
378            android:process=":p14"
378-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:398:13-35
379            android:taskAffinity="com.lody.virtual.vt"
379-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:399:13-55
380            android:theme="@style/VATheme" />
380-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:400:13-43
381        <activity
381-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:402:9-407:46
382            android:name="com.lody.virtual.client.stub.StubActivity$C15"
382-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:403:13-73
383            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
383-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:404:13-170
384            android:process=":p15"
384-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:405:13-35
385            android:taskAffinity="com.lody.virtual.vt"
385-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:406:13-55
386            android:theme="@style/VATheme" />
386-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:407:13-43
387        <activity
387-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:409:9-414:46
388            android:name="com.lody.virtual.client.stub.StubActivity$C16"
388-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:410:13-73
389            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
389-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:411:13-170
390            android:process=":p16"
390-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:412:13-35
391            android:taskAffinity="com.lody.virtual.vt"
391-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:413:13-55
392            android:theme="@style/VATheme" />
392-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:414:13-43
393        <activity
393-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:416:9-421:46
394            android:name="com.lody.virtual.client.stub.StubActivity$C17"
394-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:417:13-73
395            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
395-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:418:13-170
396            android:process=":p17"
396-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:419:13-35
397            android:taskAffinity="com.lody.virtual.vt"
397-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:420:13-55
398            android:theme="@style/VATheme" />
398-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:421:13-43
399        <activity
399-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:423:9-428:46
400            android:name="com.lody.virtual.client.stub.StubActivity$C18"
400-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:424:13-73
401            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
401-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:425:13-170
402            android:process=":p18"
402-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:426:13-35
403            android:taskAffinity="com.lody.virtual.vt"
403-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:427:13-55
404            android:theme="@style/VATheme" />
404-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:428:13-43
405        <activity
405-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:430:9-435:46
406            android:name="com.lody.virtual.client.stub.StubActivity$C19"
406-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:431:13-73
407            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
407-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:432:13-170
408            android:process=":p19"
408-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:433:13-35
409            android:taskAffinity="com.lody.virtual.vt"
409-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:434:13-55
410            android:theme="@style/VATheme" />
410-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:435:13-43
411        <activity
411-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:437:9-442:46
412            android:name="com.lody.virtual.client.stub.StubActivity$C20"
412-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:438:13-73
413            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
413-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:439:13-170
414            android:process=":p20"
414-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:440:13-35
415            android:taskAffinity="com.lody.virtual.vt"
415-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:441:13-55
416            android:theme="@style/VATheme" />
416-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:442:13-43
417        <activity
417-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:444:9-449:46
418            android:name="com.lody.virtual.client.stub.StubActivity$C21"
418-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:445:13-73
419            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
419-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:446:13-170
420            android:process=":p21"
420-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:447:13-35
421            android:taskAffinity="com.lody.virtual.vt"
421-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:448:13-55
422            android:theme="@style/VATheme" />
422-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:449:13-43
423        <activity
423-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:451:9-456:46
424            android:name="com.lody.virtual.client.stub.StubActivity$C22"
424-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:452:13-73
425            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
425-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:453:13-170
426            android:process=":p22"
426-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:454:13-35
427            android:taskAffinity="com.lody.virtual.vt"
427-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:455:13-55
428            android:theme="@style/VATheme" />
428-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:456:13-43
429        <activity
429-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:458:9-463:46
430            android:name="com.lody.virtual.client.stub.StubActivity$C23"
430-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:459:13-73
431            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
431-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:460:13-170
432            android:process=":p23"
432-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:461:13-35
433            android:taskAffinity="com.lody.virtual.vt"
433-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:462:13-55
434            android:theme="@style/VATheme" />
434-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:463:13-43
435        <activity
435-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:465:9-470:46
436            android:name="com.lody.virtual.client.stub.StubActivity$C24"
436-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:466:13-73
437            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
437-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:467:13-170
438            android:process=":p24"
438-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:468:13-35
439            android:taskAffinity="com.lody.virtual.vt"
439-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:469:13-55
440            android:theme="@style/VATheme" />
440-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:470:13-43
441        <activity
441-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:472:9-477:46
442            android:name="com.lody.virtual.client.stub.StubActivity$C25"
442-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:473:13-73
443            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
443-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:474:13-170
444            android:process=":p25"
444-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:475:13-35
445            android:taskAffinity="com.lody.virtual.vt"
445-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:476:13-55
446            android:theme="@style/VATheme" />
446-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:477:13-43
447        <activity
447-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:479:9-484:46
448            android:name="com.lody.virtual.client.stub.StubActivity$C26"
448-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:480:13-73
449            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
449-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:481:13-170
450            android:process=":p26"
450-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:482:13-35
451            android:taskAffinity="com.lody.virtual.vt"
451-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:483:13-55
452            android:theme="@style/VATheme" />
452-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:484:13-43
453        <activity
453-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:486:9-491:46
454            android:name="com.lody.virtual.client.stub.StubActivity$C27"
454-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:487:13-73
455            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
455-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:488:13-170
456            android:process=":p27"
456-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:489:13-35
457            android:taskAffinity="com.lody.virtual.vt"
457-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:490:13-55
458            android:theme="@style/VATheme" />
458-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:491:13-43
459        <activity
459-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:493:9-498:46
460            android:name="com.lody.virtual.client.stub.StubActivity$C28"
460-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:494:13-73
461            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
461-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:495:13-170
462            android:process=":p28"
462-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:496:13-35
463            android:taskAffinity="com.lody.virtual.vt"
463-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:497:13-55
464            android:theme="@style/VATheme" />
464-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:498:13-43
465        <activity
465-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:500:9-505:46
466            android:name="com.lody.virtual.client.stub.StubActivity$C29"
466-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:501:13-73
467            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
467-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:502:13-170
468            android:process=":p29"
468-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:503:13-35
469            android:taskAffinity="com.lody.virtual.vt"
469-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:504:13-55
470            android:theme="@style/VATheme" />
470-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:505:13-43
471        <activity
471-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:507:9-512:46
472            android:name="com.lody.virtual.client.stub.StubActivity$C30"
472-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:508:13-73
473            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
473-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:509:13-170
474            android:process=":p30"
474-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:510:13-35
475            android:taskAffinity="com.lody.virtual.vt"
475-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:511:13-55
476            android:theme="@style/VATheme" />
476-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:512:13-43
477        <activity
477-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:514:9-519:46
478            android:name="com.lody.virtual.client.stub.StubActivity$C31"
478-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:515:13-73
479            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
479-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:516:13-170
480            android:process=":p31"
480-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:517:13-35
481            android:taskAffinity="com.lody.virtual.vt"
481-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:518:13-55
482            android:theme="@style/VATheme" />
482-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:519:13-43
483        <activity
483-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:521:9-526:46
484            android:name="com.lody.virtual.client.stub.StubActivity$C32"
484-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:522:13-73
485            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
485-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:523:13-170
486            android:process=":p32"
486-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:524:13-35
487            android:taskAffinity="com.lody.virtual.vt"
487-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:525:13-55
488            android:theme="@style/VATheme" />
488-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:526:13-43
489        <activity
489-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:528:9-533:46
490            android:name="com.lody.virtual.client.stub.StubActivity$C33"
490-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:529:13-73
491            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
491-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:530:13-170
492            android:process=":p33"
492-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:531:13-35
493            android:taskAffinity="com.lody.virtual.vt"
493-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:532:13-55
494            android:theme="@style/VATheme" />
494-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:533:13-43
495        <activity
495-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:535:9-541:46
496            android:name="com.lody.virtual.client.stub.StubActivity$C34"
496-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:536:13-73
497            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
497-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:537:13-170
498            android:process=":p34"
498-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:539:13-35
499            android:taskAffinity="com.lody.virtual.vt"
499-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:540:13-55
500            android:theme="@style/VATheme" />
500-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:541:13-43
501        <activity
501-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:543:9-548:46
502            android:name="com.lody.virtual.client.stub.StubActivity$C35"
502-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:544:13-73
503            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
503-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:545:13-170
504            android:process=":p35"
504-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:546:13-35
505            android:taskAffinity="com.lody.virtual.vt"
505-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:547:13-55
506            android:theme="@style/VATheme" />
506-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:548:13-43
507        <activity
507-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:550:9-555:46
508            android:name="com.lody.virtual.client.stub.StubActivity$C36"
508-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:551:13-73
509            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
509-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:552:13-170
510            android:process=":p36"
510-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:553:13-35
511            android:taskAffinity="com.lody.virtual.vt"
511-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:554:13-55
512            android:theme="@style/VATheme" />
512-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:555:13-43
513        <activity
513-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:557:9-562:46
514            android:name="com.lody.virtual.client.stub.StubActivity$C37"
514-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:558:13-73
515            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
515-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:559:13-170
516            android:process=":p37"
516-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:560:13-35
517            android:taskAffinity="com.lody.virtual.vt"
517-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:561:13-55
518            android:theme="@style/VATheme" />
518-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:562:13-43
519        <activity
519-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:564:9-569:46
520            android:name="com.lody.virtual.client.stub.StubActivity$C38"
520-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:565:13-73
521            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
521-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:566:13-170
522            android:process=":p38"
522-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:567:13-35
523            android:taskAffinity="com.lody.virtual.vt"
523-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:568:13-55
524            android:theme="@style/VATheme" />
524-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:569:13-43
525        <activity
525-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:571:9-576:46
526            android:name="com.lody.virtual.client.stub.StubActivity$C39"
526-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:572:13-73
527            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
527-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:573:13-170
528            android:process=":p39"
528-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:574:13-35
529            android:taskAffinity="com.lody.virtual.vt"
529-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:575:13-55
530            android:theme="@style/VATheme" />
530-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:576:13-43
531        <activity
531-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:578:9-583:46
532            android:name="com.lody.virtual.client.stub.StubActivity$C40"
532-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:579:13-73
533            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
533-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:580:13-170
534            android:process=":p40"
534-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:581:13-35
535            android:taskAffinity="com.lody.virtual.vt"
535-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:582:13-55
536            android:theme="@style/VATheme" />
536-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:583:13-43
537        <activity
537-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:585:9-590:46
538            android:name="com.lody.virtual.client.stub.StubActivity$C41"
538-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:586:13-73
539            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
539-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:587:13-170
540            android:process=":p41"
540-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:588:13-35
541            android:taskAffinity="com.lody.virtual.vt"
541-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:589:13-55
542            android:theme="@style/VATheme" />
542-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:590:13-43
543        <activity
543-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:592:9-597:46
544            android:name="com.lody.virtual.client.stub.StubActivity$C42"
544-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:593:13-73
545            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
545-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:594:13-170
546            android:process=":p42"
546-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:595:13-35
547            android:taskAffinity="com.lody.virtual.vt"
547-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:596:13-55
548            android:theme="@style/VATheme" />
548-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:597:13-43
549        <activity
549-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:599:9-604:46
550            android:name="com.lody.virtual.client.stub.StubActivity$C43"
550-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:600:13-73
551            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
551-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:601:13-170
552            android:process=":p43"
552-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:602:13-35
553            android:taskAffinity="com.lody.virtual.vt"
553-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:603:13-55
554            android:theme="@style/VATheme" />
554-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:604:13-43
555        <activity
555-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:606:9-611:46
556            android:name="com.lody.virtual.client.stub.StubActivity$C44"
556-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:607:13-73
557            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
557-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:608:13-170
558            android:process=":p44"
558-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:609:13-35
559            android:taskAffinity="com.lody.virtual.vt"
559-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:610:13-55
560            android:theme="@style/VATheme" />
560-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:611:13-43
561        <activity
561-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:613:9-618:46
562            android:name="com.lody.virtual.client.stub.StubActivity$C45"
562-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:614:13-73
563            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
563-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:615:13-170
564            android:process=":p45"
564-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:616:13-35
565            android:taskAffinity="com.lody.virtual.vt"
565-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:617:13-55
566            android:theme="@style/VATheme" />
566-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:618:13-43
567        <activity
567-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:620:9-625:46
568            android:name="com.lody.virtual.client.stub.StubActivity$C46"
568-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:621:13-73
569            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
569-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:622:13-170
570            android:process=":p46"
570-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:623:13-35
571            android:taskAffinity="com.lody.virtual.vt"
571-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:624:13-55
572            android:theme="@style/VATheme" />
572-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:625:13-43
573        <activity
573-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:627:9-632:46
574            android:name="com.lody.virtual.client.stub.StubActivity$C47"
574-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:628:13-73
575            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
575-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:629:13-170
576            android:process=":p47"
576-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:630:13-35
577            android:taskAffinity="com.lody.virtual.vt"
577-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:631:13-55
578            android:theme="@style/VATheme" />
578-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:632:13-43
579        <activity
579-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:634:9-639:46
580            android:name="com.lody.virtual.client.stub.StubActivity$C48"
580-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:635:13-73
581            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
581-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:636:13-170
582            android:process=":p48"
582-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:637:13-35
583            android:taskAffinity="com.lody.virtual.vt"
583-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:638:13-55
584            android:theme="@style/VATheme" />
584-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:639:13-43
585        <activity
585-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:641:9-646:46
586            android:name="com.lody.virtual.client.stub.StubActivity$C49"
586-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:642:13-73
587            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
587-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:643:13-170
588            android:process=":p49"
588-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:644:13-35
589            android:taskAffinity="com.lody.virtual.vt"
589-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:645:13-55
590            android:theme="@style/VATheme" />
590-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:646:13-43
591        <activity
591-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:648:9-653:59
592            android:name="com.lody.virtual.client.stub.StubDialog$C0"
592-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:649:13-70
593            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
593-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:650:13-170
594            android:process=":p0"
594-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:651:13-34
595            android:taskAffinity="com.lody.virtual.vt"
595-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:652:13-55
596            android:theme="@android:style/Theme.Dialog" />
596-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:653:13-56
597        <activity
597-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:655:9-660:59
598            android:name="com.lody.virtual.client.stub.StubDialog$C1"
598-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:656:13-70
599            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
599-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:657:13-170
600            android:process=":p1"
600-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:658:13-34
601            android:taskAffinity="com.lody.virtual.vt"
601-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:659:13-55
602            android:theme="@android:style/Theme.Dialog" />
602-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:660:13-56
603        <activity
603-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:662:9-667:59
604            android:name="com.lody.virtual.client.stub.StubDialog$C2"
604-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:663:13-70
605            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
605-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:664:13-170
606            android:process=":p2"
606-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:665:13-34
607            android:taskAffinity="com.lody.virtual.vt"
607-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:666:13-55
608            android:theme="@android:style/Theme.Dialog" />
608-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:667:13-56
609        <activity
609-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:669:9-674:59
610            android:name="com.lody.virtual.client.stub.StubDialog$C3"
610-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:670:13-70
611            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
611-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:671:13-170
612            android:process=":p3"
612-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:672:13-34
613            android:taskAffinity="com.lody.virtual.vt"
613-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:673:13-55
614            android:theme="@android:style/Theme.Dialog" />
614-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:674:13-56
615        <activity
615-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:676:9-681:59
616            android:name="com.lody.virtual.client.stub.StubDialog$C4"
616-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:677:13-70
617            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
617-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:678:13-170
618            android:process=":p4"
618-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:679:13-34
619            android:taskAffinity="com.lody.virtual.vt"
619-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:680:13-55
620            android:theme="@android:style/Theme.Dialog" />
620-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:681:13-56
621        <activity
621-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:683:9-688:59
622            android:name="com.lody.virtual.client.stub.StubDialog$C5"
622-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:684:13-70
623            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
623-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:685:13-170
624            android:process=":p5"
624-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:686:13-34
625            android:taskAffinity="com.lody.virtual.vt"
625-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:687:13-55
626            android:theme="@android:style/Theme.Dialog" />
626-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:688:13-56
627        <activity
627-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:690:9-695:59
628            android:name="com.lody.virtual.client.stub.StubDialog$C6"
628-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:691:13-70
629            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
629-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:692:13-170
630            android:process=":p6"
630-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:693:13-34
631            android:taskAffinity="com.lody.virtual.vt"
631-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:694:13-55
632            android:theme="@android:style/Theme.Dialog" />
632-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:695:13-56
633        <activity
633-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:697:9-702:59
634            android:name="com.lody.virtual.client.stub.StubDialog$C7"
634-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:698:13-70
635            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
635-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:699:13-170
636            android:process=":p7"
636-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:700:13-34
637            android:taskAffinity="com.lody.virtual.vt"
637-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:701:13-55
638            android:theme="@android:style/Theme.Dialog" />
638-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:702:13-56
639        <activity
639-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:704:9-709:59
640            android:name="com.lody.virtual.client.stub.StubDialog$C8"
640-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:705:13-70
641            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
641-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:706:13-170
642            android:process=":p8"
642-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:707:13-34
643            android:taskAffinity="com.lody.virtual.vt"
643-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:708:13-55
644            android:theme="@android:style/Theme.Dialog" />
644-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:709:13-56
645        <activity
645-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:711:9-716:59
646            android:name="com.lody.virtual.client.stub.StubDialog$C9"
646-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:712:13-70
647            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
647-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:713:13-170
648            android:process=":p9"
648-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:714:13-34
649            android:taskAffinity="com.lody.virtual.vt"
649-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:715:13-55
650            android:theme="@android:style/Theme.Dialog" />
650-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:716:13-56
651        <activity
651-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:718:9-723:59
652            android:name="com.lody.virtual.client.stub.StubDialog$C10"
652-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:719:13-71
653            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
653-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:720:13-170
654            android:process=":p10"
654-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:721:13-35
655            android:taskAffinity="com.lody.virtual.vt"
655-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:722:13-55
656            android:theme="@android:style/Theme.Dialog" />
656-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:723:13-56
657        <activity
657-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:725:9-730:59
658            android:name="com.lody.virtual.client.stub.StubDialog$C11"
658-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:726:13-71
659            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
659-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:727:13-170
660            android:process=":p11"
660-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:728:13-35
661            android:taskAffinity="com.lody.virtual.vt"
661-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:729:13-55
662            android:theme="@android:style/Theme.Dialog" />
662-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:730:13-56
663        <activity
663-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:732:9-737:59
664            android:name="com.lody.virtual.client.stub.StubDialog$C12"
664-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:733:13-71
665            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
665-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:734:13-170
666            android:process=":p12"
666-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:735:13-35
667            android:taskAffinity="com.lody.virtual.vt"
667-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:736:13-55
668            android:theme="@android:style/Theme.Dialog" />
668-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:737:13-56
669        <activity
669-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:739:9-744:59
670            android:name="com.lody.virtual.client.stub.StubDialog$C13"
670-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:740:13-71
671            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
671-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:741:13-170
672            android:process=":p13"
672-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:742:13-35
673            android:taskAffinity="com.lody.virtual.vt"
673-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:743:13-55
674            android:theme="@android:style/Theme.Dialog" />
674-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:744:13-56
675        <activity
675-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:746:9-751:59
676            android:name="com.lody.virtual.client.stub.StubDialog$C14"
676-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:747:13-71
677            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
677-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:748:13-170
678            android:process=":p14"
678-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:749:13-35
679            android:taskAffinity="com.lody.virtual.vt"
679-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:750:13-55
680            android:theme="@android:style/Theme.Dialog" />
680-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:751:13-56
681        <activity
681-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:753:9-758:59
682            android:name="com.lody.virtual.client.stub.StubDialog$C15"
682-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:754:13-71
683            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
683-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:755:13-170
684            android:process=":p15"
684-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:756:13-35
685            android:taskAffinity="com.lody.virtual.vt"
685-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:757:13-55
686            android:theme="@android:style/Theme.Dialog" />
686-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:758:13-56
687        <activity
687-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:760:9-765:59
688            android:name="com.lody.virtual.client.stub.StubDialog$C16"
688-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:761:13-71
689            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
689-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:762:13-170
690            android:process=":p16"
690-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:763:13-35
691            android:taskAffinity="com.lody.virtual.vt"
691-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:764:13-55
692            android:theme="@android:style/Theme.Dialog" />
692-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:765:13-56
693        <activity
693-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:767:9-772:59
694            android:name="com.lody.virtual.client.stub.StubDialog$C17"
694-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:768:13-71
695            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
695-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:769:13-170
696            android:process=":p17"
696-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:770:13-35
697            android:taskAffinity="com.lody.virtual.vt"
697-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:771:13-55
698            android:theme="@android:style/Theme.Dialog" />
698-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:772:13-56
699        <activity
699-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:774:9-779:59
700            android:name="com.lody.virtual.client.stub.StubDialog$C18"
700-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:775:13-71
701            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
701-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:776:13-170
702            android:process=":p18"
702-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:777:13-35
703            android:taskAffinity="com.lody.virtual.vt"
703-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:778:13-55
704            android:theme="@android:style/Theme.Dialog" />
704-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:779:13-56
705        <activity
705-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:781:9-786:59
706            android:name="com.lody.virtual.client.stub.StubDialog$C19"
706-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:782:13-71
707            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
707-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:783:13-170
708            android:process=":p19"
708-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:784:13-35
709            android:taskAffinity="com.lody.virtual.vt"
709-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:785:13-55
710            android:theme="@android:style/Theme.Dialog" />
710-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:786:13-56
711        <activity
711-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:788:9-793:59
712            android:name="com.lody.virtual.client.stub.StubDialog$C20"
712-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:789:13-71
713            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
713-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:790:13-170
714            android:process=":p20"
714-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:791:13-35
715            android:taskAffinity="com.lody.virtual.vt"
715-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:792:13-55
716            android:theme="@android:style/Theme.Dialog" />
716-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:793:13-56
717        <activity
717-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:795:9-800:59
718            android:name="com.lody.virtual.client.stub.StubDialog$C21"
718-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:796:13-71
719            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
719-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:797:13-170
720            android:process=":p21"
720-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:798:13-35
721            android:taskAffinity="com.lody.virtual.vt"
721-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:799:13-55
722            android:theme="@android:style/Theme.Dialog" />
722-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:800:13-56
723        <activity
723-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:802:9-807:59
724            android:name="com.lody.virtual.client.stub.StubDialog$C22"
724-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:803:13-71
725            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
725-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:804:13-170
726            android:process=":p22"
726-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:805:13-35
727            android:taskAffinity="com.lody.virtual.vt"
727-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:806:13-55
728            android:theme="@android:style/Theme.Dialog" />
728-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:807:13-56
729        <activity
729-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:809:9-814:59
730            android:name="com.lody.virtual.client.stub.StubDialog$C23"
730-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:810:13-71
731            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
731-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:811:13-170
732            android:process=":p23"
732-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:812:13-35
733            android:taskAffinity="com.lody.virtual.vt"
733-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:813:13-55
734            android:theme="@android:style/Theme.Dialog" />
734-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:814:13-56
735        <activity
735-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:816:9-821:59
736            android:name="com.lody.virtual.client.stub.StubDialog$C24"
736-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:817:13-71
737            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
737-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:818:13-170
738            android:process=":p24"
738-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:819:13-35
739            android:taskAffinity="com.lody.virtual.vt"
739-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:820:13-55
740            android:theme="@android:style/Theme.Dialog" />
740-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:821:13-56
741        <activity
741-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:823:9-828:59
742            android:name="com.lody.virtual.client.stub.StubDialog$C25"
742-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:824:13-71
743            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
743-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:825:13-170
744            android:process=":p25"
744-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:826:13-35
745            android:taskAffinity="com.lody.virtual.vt"
745-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:827:13-55
746            android:theme="@android:style/Theme.Dialog" />
746-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:828:13-56
747        <activity
747-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:830:9-835:59
748            android:name="com.lody.virtual.client.stub.StubDialog$C26"
748-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:831:13-71
749            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
749-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:832:13-170
750            android:process=":p26"
750-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:833:13-35
751            android:taskAffinity="com.lody.virtual.vt"
751-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:834:13-55
752            android:theme="@android:style/Theme.Dialog" />
752-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:835:13-56
753        <activity
753-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:837:9-842:59
754            android:name="com.lody.virtual.client.stub.StubDialog$C27"
754-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:838:13-71
755            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
755-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:839:13-170
756            android:process=":p27"
756-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:840:13-35
757            android:taskAffinity="com.lody.virtual.vt"
757-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:841:13-55
758            android:theme="@android:style/Theme.Dialog" />
758-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:842:13-56
759        <activity
759-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:844:9-849:59
760            android:name="com.lody.virtual.client.stub.StubDialog$C28"
760-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:845:13-71
761            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
761-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:846:13-170
762            android:process=":p28"
762-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:847:13-35
763            android:taskAffinity="com.lody.virtual.vt"
763-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:848:13-55
764            android:theme="@android:style/Theme.Dialog" />
764-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:849:13-56
765        <activity
765-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:851:9-856:59
766            android:name="com.lody.virtual.client.stub.StubDialog$C29"
766-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:852:13-71
767            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
767-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:853:13-170
768            android:process=":p29"
768-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:854:13-35
769            android:taskAffinity="com.lody.virtual.vt"
769-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:855:13-55
770            android:theme="@android:style/Theme.Dialog" />
770-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:856:13-56
771        <activity
771-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:858:9-863:59
772            android:name="com.lody.virtual.client.stub.StubDialog$C30"
772-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:859:13-71
773            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
773-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:860:13-170
774            android:process=":p30"
774-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:861:13-35
775            android:taskAffinity="com.lody.virtual.vt"
775-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:862:13-55
776            android:theme="@android:style/Theme.Dialog" />
776-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:863:13-56
777        <activity
777-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:865:9-870:59
778            android:name="com.lody.virtual.client.stub.StubDialog$C31"
778-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:866:13-71
779            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
779-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:867:13-170
780            android:process=":p31"
780-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:868:13-35
781            android:taskAffinity="com.lody.virtual.vt"
781-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:869:13-55
782            android:theme="@android:style/Theme.Dialog" />
782-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:870:13-56
783        <activity
783-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:872:9-877:59
784            android:name="com.lody.virtual.client.stub.StubDialog$C32"
784-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:873:13-71
785            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
785-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:874:13-170
786            android:process=":p32"
786-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:875:13-35
787            android:taskAffinity="com.lody.virtual.vt"
787-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:876:13-55
788            android:theme="@android:style/Theme.Dialog" />
788-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:877:13-56
789        <activity
789-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:879:9-884:59
790            android:name="com.lody.virtual.client.stub.StubDialog$C33"
790-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:880:13-71
791            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
791-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:881:13-170
792            android:process=":p33"
792-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:882:13-35
793            android:taskAffinity="com.lody.virtual.vt"
793-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:883:13-55
794            android:theme="@android:style/Theme.Dialog" />
794-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:884:13-56
795        <activity
795-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:886:9-891:59
796            android:name="com.lody.virtual.client.stub.StubDialog$C34"
796-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:887:13-71
797            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
797-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:888:13-170
798            android:process=":p34"
798-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:889:13-35
799            android:taskAffinity="com.lody.virtual.vt"
799-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:890:13-55
800            android:theme="@android:style/Theme.Dialog" />
800-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:891:13-56
801        <activity
801-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:893:9-898:59
802            android:name="com.lody.virtual.client.stub.StubDialog$C35"
802-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:894:13-71
803            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
803-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:895:13-170
804            android:process=":p35"
804-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:896:13-35
805            android:taskAffinity="com.lody.virtual.vt"
805-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:897:13-55
806            android:theme="@android:style/Theme.Dialog" />
806-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:898:13-56
807        <activity
807-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:900:9-905:59
808            android:name="com.lody.virtual.client.stub.StubDialog$C36"
808-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:901:13-71
809            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
809-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:902:13-170
810            android:process=":p36"
810-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:903:13-35
811            android:taskAffinity="com.lody.virtual.vt"
811-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:904:13-55
812            android:theme="@android:style/Theme.Dialog" />
812-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:905:13-56
813        <activity
813-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:907:9-912:59
814            android:name="com.lody.virtual.client.stub.StubDialog$C37"
814-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:908:13-71
815            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
815-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:909:13-170
816            android:process=":p37"
816-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:910:13-35
817            android:taskAffinity="com.lody.virtual.vt"
817-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:911:13-55
818            android:theme="@android:style/Theme.Dialog" />
818-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:912:13-56
819        <activity
819-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:914:9-919:59
820            android:name="com.lody.virtual.client.stub.StubDialog$C38"
820-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:915:13-71
821            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
821-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:916:13-170
822            android:process=":p38"
822-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:917:13-35
823            android:taskAffinity="com.lody.virtual.vt"
823-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:918:13-55
824            android:theme="@android:style/Theme.Dialog" />
824-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:919:13-56
825        <activity
825-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:921:9-926:59
826            android:name="com.lody.virtual.client.stub.StubDialog$C39"
826-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:922:13-71
827            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
827-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:923:13-170
828            android:process=":p39"
828-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:924:13-35
829            android:taskAffinity="com.lody.virtual.vt"
829-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:925:13-55
830            android:theme="@android:style/Theme.Dialog" />
830-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:926:13-56
831        <activity
831-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:928:9-933:59
832            android:name="com.lody.virtual.client.stub.StubDialog$C40"
832-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:929:13-71
833            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
833-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:930:13-170
834            android:process=":p40"
834-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:931:13-35
835            android:taskAffinity="com.lody.virtual.vt"
835-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:932:13-55
836            android:theme="@android:style/Theme.Dialog" />
836-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:933:13-56
837        <activity
837-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:935:9-940:59
838            android:name="com.lody.virtual.client.stub.StubDialog$C41"
838-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:936:13-71
839            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
839-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:937:13-170
840            android:process=":p41"
840-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:938:13-35
841            android:taskAffinity="com.lody.virtual.vt"
841-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:939:13-55
842            android:theme="@android:style/Theme.Dialog" />
842-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:940:13-56
843        <activity
843-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:942:9-947:59
844            android:name="com.lody.virtual.client.stub.StubDialog$C42"
844-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:943:13-71
845            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
845-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:944:13-170
846            android:process=":p42"
846-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:945:13-35
847            android:taskAffinity="com.lody.virtual.vt"
847-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:946:13-55
848            android:theme="@android:style/Theme.Dialog" />
848-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:947:13-56
849        <activity
849-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:949:9-954:59
850            android:name="com.lody.virtual.client.stub.StubDialog$C43"
850-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:950:13-71
851            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
851-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:951:13-170
852            android:process=":p43"
852-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:952:13-35
853            android:taskAffinity="com.lody.virtual.vt"
853-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:953:13-55
854            android:theme="@android:style/Theme.Dialog" />
854-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:954:13-56
855        <activity
855-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:956:9-961:59
856            android:name="com.lody.virtual.client.stub.StubDialog$C44"
856-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:957:13-71
857            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
857-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:958:13-170
858            android:process=":p44"
858-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:959:13-35
859            android:taskAffinity="com.lody.virtual.vt"
859-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:960:13-55
860            android:theme="@android:style/Theme.Dialog" />
860-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:961:13-56
861        <activity
861-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:963:9-968:59
862            android:name="com.lody.virtual.client.stub.StubDialog$C45"
862-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:964:13-71
863            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
863-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:965:13-170
864            android:process=":p45"
864-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:966:13-35
865            android:taskAffinity="com.lody.virtual.vt"
865-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:967:13-55
866            android:theme="@android:style/Theme.Dialog" />
866-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:968:13-56
867        <activity
867-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:970:9-975:59
868            android:name="com.lody.virtual.client.stub.StubDialog$C46"
868-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:971:13-71
869            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
869-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:972:13-170
870            android:process=":p46"
870-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:973:13-35
871            android:taskAffinity="com.lody.virtual.vt"
871-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:974:13-55
872            android:theme="@android:style/Theme.Dialog" />
872-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:975:13-56
873        <activity
873-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:977:9-982:59
874            android:name="com.lody.virtual.client.stub.StubDialog$C47"
874-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:978:13-71
875            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
875-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:979:13-170
876            android:process=":p47"
876-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:980:13-35
877            android:taskAffinity="com.lody.virtual.vt"
877-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:981:13-55
878            android:theme="@android:style/Theme.Dialog" />
878-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:982:13-56
879        <activity
879-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:984:9-989:59
880            android:name="com.lody.virtual.client.stub.StubDialog$C48"
880-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:985:13-71
881            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
881-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:986:13-170
882            android:process=":p48"
882-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:987:13-35
883            android:taskAffinity="com.lody.virtual.vt"
883-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:988:13-55
884            android:theme="@android:style/Theme.Dialog" />
884-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:989:13-56
885        <activity
885-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:991:9-996:59
886            android:name="com.lody.virtual.client.stub.StubDialog$C49"
886-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:992:13-71
887            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
887-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:993:13-170
888            android:process=":p49"
888-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:994:13-35
889            android:taskAffinity="com.lody.virtual.vt"
889-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:995:13-55
890            android:theme="@android:style/Theme.Dialog" />
890-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:996:13-56
891
892        <provider
892-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:998:9-1002:37
893            android:name="com.lody.virtual.client.stub.StubContentProvider$C0"
893-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:999:13-79
894            android:authorities="${applicationId}.virtual_stub_0"
894-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1000:13-66
895            android:exported="false"
895-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1001:13-37
896            android:process=":p0" />
896-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1002:13-34
897        <provider
897-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1004:9-1008:37
898            android:name="com.lody.virtual.client.stub.StubContentProvider$C1"
898-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1005:13-79
899            android:authorities="${applicationId}.virtual_stub_1"
899-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1006:13-66
900            android:exported="false"
900-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1007:13-37
901            android:process=":p1" />
901-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1008:13-34
902        <provider
902-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1010:9-1014:37
903            android:name="com.lody.virtual.client.stub.StubContentProvider$C2"
903-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1011:13-79
904            android:authorities="${applicationId}.virtual_stub_2"
904-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1012:13-66
905            android:exported="false"
905-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1013:13-37
906            android:process=":p2" />
906-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1014:13-34
907        <provider
907-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1016:9-1020:37
908            android:name="com.lody.virtual.client.stub.StubContentProvider$C3"
908-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1017:13-79
909            android:authorities="${applicationId}.virtual_stub_3"
909-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1018:13-66
910            android:exported="false"
910-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1019:13-37
911            android:process=":p3" />
911-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1020:13-34
912        <provider
912-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1022:9-1026:37
913            android:name="com.lody.virtual.client.stub.StubContentProvider$C4"
913-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1023:13-79
914            android:authorities="${applicationId}.virtual_stub_4"
914-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1024:13-66
915            android:exported="false"
915-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1025:13-37
916            android:process=":p4" />
916-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1026:13-34
917        <provider
917-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1028:9-1032:37
918            android:name="com.lody.virtual.client.stub.StubContentProvider$C5"
918-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1029:13-79
919            android:authorities="${applicationId}.virtual_stub_5"
919-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1030:13-66
920            android:exported="false"
920-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1031:13-37
921            android:process=":p5" />
921-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1032:13-34
922        <provider
922-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1034:9-1038:37
923            android:name="com.lody.virtual.client.stub.StubContentProvider$C6"
923-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1035:13-79
924            android:authorities="${applicationId}.virtual_stub_6"
924-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1036:13-66
925            android:exported="false"
925-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1037:13-37
926            android:process=":p6" />
926-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1038:13-34
927        <provider
927-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1040:9-1044:37
928            android:name="com.lody.virtual.client.stub.StubContentProvider$C7"
928-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1041:13-79
929            android:authorities="${applicationId}.virtual_stub_7"
929-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1042:13-66
930            android:exported="false"
930-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1043:13-37
931            android:process=":p7" />
931-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1044:13-34
932        <provider
932-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1046:9-1050:37
933            android:name="com.lody.virtual.client.stub.StubContentProvider$C8"
933-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1047:13-79
934            android:authorities="${applicationId}.virtual_stub_8"
934-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1048:13-66
935            android:exported="false"
935-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1049:13-37
936            android:process=":p8" />
936-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1050:13-34
937        <provider
937-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1052:9-1056:37
938            android:name="com.lody.virtual.client.stub.StubContentProvider$C9"
938-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1053:13-79
939            android:authorities="${applicationId}.virtual_stub_9"
939-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1054:13-66
940            android:exported="false"
940-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1055:13-37
941            android:process=":p9" />
941-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1056:13-34
942        <provider
942-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1058:9-1062:38
943            android:name="com.lody.virtual.client.stub.StubContentProvider$C10"
943-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1059:13-80
944            android:authorities="${applicationId}.virtual_stub_10"
944-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1060:13-67
945            android:exported="false"
945-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1061:13-37
946            android:process=":p10" />
946-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1062:13-35
947        <provider
947-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1064:9-1068:38
948            android:name="com.lody.virtual.client.stub.StubContentProvider$C11"
948-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1065:13-80
949            android:authorities="${applicationId}.virtual_stub_11"
949-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1066:13-67
950            android:exported="false"
950-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1067:13-37
951            android:process=":p11" />
951-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1068:13-35
952        <provider
952-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1070:9-1074:38
953            android:name="com.lody.virtual.client.stub.StubContentProvider$C12"
953-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1071:13-80
954            android:authorities="${applicationId}.virtual_stub_12"
954-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1072:13-67
955            android:exported="false"
955-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1073:13-37
956            android:process=":p12" />
956-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1074:13-35
957        <provider
957-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1076:9-1080:38
958            android:name="com.lody.virtual.client.stub.StubContentProvider$C13"
958-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1077:13-80
959            android:authorities="${applicationId}.virtual_stub_13"
959-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1078:13-67
960            android:exported="false"
960-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1079:13-37
961            android:process=":p13" />
961-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1080:13-35
962        <provider
962-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1082:9-1086:38
963            android:name="com.lody.virtual.client.stub.StubContentProvider$C14"
963-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1083:13-80
964            android:authorities="${applicationId}.virtual_stub_14"
964-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1084:13-67
965            android:exported="false"
965-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1085:13-37
966            android:process=":p14" />
966-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1086:13-35
967        <provider
967-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1088:9-1092:38
968            android:name="com.lody.virtual.client.stub.StubContentProvider$C15"
968-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1089:13-80
969            android:authorities="${applicationId}.virtual_stub_15"
969-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1090:13-67
970            android:exported="false"
970-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1091:13-37
971            android:process=":p15" />
971-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1092:13-35
972        <provider
972-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1094:9-1098:38
973            android:name="com.lody.virtual.client.stub.StubContentProvider$C16"
973-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1095:13-80
974            android:authorities="${applicationId}.virtual_stub_16"
974-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1096:13-67
975            android:exported="false"
975-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1097:13-37
976            android:process=":p16" />
976-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1098:13-35
977        <provider
977-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1100:9-1104:38
978            android:name="com.lody.virtual.client.stub.StubContentProvider$C17"
978-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1101:13-80
979            android:authorities="${applicationId}.virtual_stub_17"
979-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1102:13-67
980            android:exported="false"
980-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1103:13-37
981            android:process=":p17" />
981-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1104:13-35
982        <provider
982-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1106:9-1110:38
983            android:name="com.lody.virtual.client.stub.StubContentProvider$C18"
983-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1107:13-80
984            android:authorities="${applicationId}.virtual_stub_18"
984-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1108:13-67
985            android:exported="false"
985-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1109:13-37
986            android:process=":p18" />
986-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1110:13-35
987        <provider
987-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1112:9-1116:38
988            android:name="com.lody.virtual.client.stub.StubContentProvider$C19"
988-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1113:13-80
989            android:authorities="${applicationId}.virtual_stub_19"
989-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1114:13-67
990            android:exported="false"
990-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1115:13-37
991            android:process=":p19" />
991-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1116:13-35
992        <provider
992-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1118:9-1122:38
993            android:name="com.lody.virtual.client.stub.StubContentProvider$C20"
993-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1119:13-80
994            android:authorities="${applicationId}.virtual_stub_20"
994-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1120:13-67
995            android:exported="false"
995-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1121:13-37
996            android:process=":p20" />
996-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1122:13-35
997        <provider
997-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1124:9-1128:38
998            android:name="com.lody.virtual.client.stub.StubContentProvider$C21"
998-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1125:13-80
999            android:authorities="${applicationId}.virtual_stub_21"
999-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1126:13-67
1000            android:exported="false"
1000-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1127:13-37
1001            android:process=":p21" />
1001-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1128:13-35
1002        <provider
1002-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1130:9-1134:38
1003            android:name="com.lody.virtual.client.stub.StubContentProvider$C22"
1003-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1131:13-80
1004            android:authorities="${applicationId}.virtual_stub_22"
1004-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1132:13-67
1005            android:exported="false"
1005-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1133:13-37
1006            android:process=":p22" />
1006-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1134:13-35
1007        <provider
1007-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1136:9-1140:38
1008            android:name="com.lody.virtual.client.stub.StubContentProvider$C23"
1008-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1137:13-80
1009            android:authorities="${applicationId}.virtual_stub_23"
1009-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1138:13-67
1010            android:exported="false"
1010-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1139:13-37
1011            android:process=":p23" />
1011-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1140:13-35
1012        <provider
1012-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1142:9-1146:38
1013            android:name="com.lody.virtual.client.stub.StubContentProvider$C24"
1013-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1143:13-80
1014            android:authorities="${applicationId}.virtual_stub_24"
1014-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1144:13-67
1015            android:exported="false"
1015-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1145:13-37
1016            android:process=":p24" />
1016-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1146:13-35
1017        <provider
1017-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1148:9-1152:38
1018            android:name="com.lody.virtual.client.stub.StubContentProvider$C25"
1018-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1149:13-80
1019            android:authorities="${applicationId}.virtual_stub_25"
1019-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1150:13-67
1020            android:exported="false"
1020-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1151:13-37
1021            android:process=":p25" />
1021-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1152:13-35
1022        <provider
1022-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1154:9-1158:38
1023            android:name="com.lody.virtual.client.stub.StubContentProvider$C26"
1023-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1155:13-80
1024            android:authorities="${applicationId}.virtual_stub_26"
1024-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1156:13-67
1025            android:exported="false"
1025-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1157:13-37
1026            android:process=":p26" />
1026-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1158:13-35
1027        <provider
1027-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1160:9-1164:38
1028            android:name="com.lody.virtual.client.stub.StubContentProvider$C27"
1028-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1161:13-80
1029            android:authorities="${applicationId}.virtual_stub_27"
1029-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1162:13-67
1030            android:exported="false"
1030-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1163:13-37
1031            android:process=":p27" />
1031-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1164:13-35
1032        <provider
1032-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1166:9-1170:38
1033            android:name="com.lody.virtual.client.stub.StubContentProvider$C28"
1033-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1167:13-80
1034            android:authorities="${applicationId}.virtual_stub_28"
1034-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1168:13-67
1035            android:exported="false"
1035-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1169:13-37
1036            android:process=":p28" />
1036-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1170:13-35
1037        <provider
1037-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1172:9-1176:38
1038            android:name="com.lody.virtual.client.stub.StubContentProvider$C29"
1038-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1173:13-80
1039            android:authorities="${applicationId}.virtual_stub_29"
1039-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1174:13-67
1040            android:exported="false"
1040-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1175:13-37
1041            android:process=":p29" />
1041-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1176:13-35
1042        <provider
1042-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1178:9-1182:38
1043            android:name="com.lody.virtual.client.stub.StubContentProvider$C30"
1043-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1179:13-80
1044            android:authorities="${applicationId}.virtual_stub_30"
1044-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1180:13-67
1045            android:exported="false"
1045-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1181:13-37
1046            android:process=":p30" />
1046-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1182:13-35
1047        <provider
1047-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1184:9-1188:38
1048            android:name="com.lody.virtual.client.stub.StubContentProvider$C31"
1048-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1185:13-80
1049            android:authorities="${applicationId}.virtual_stub_31"
1049-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1186:13-67
1050            android:exported="false"
1050-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1187:13-37
1051            android:process=":p31" />
1051-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1188:13-35
1052        <provider
1052-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1190:9-1194:38
1053            android:name="com.lody.virtual.client.stub.StubContentProvider$C32"
1053-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1191:13-80
1054            android:authorities="${applicationId}.virtual_stub_32"
1054-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1192:13-67
1055            android:exported="false"
1055-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1193:13-37
1056            android:process=":p32" />
1056-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1194:13-35
1057        <provider
1057-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1196:9-1200:38
1058            android:name="com.lody.virtual.client.stub.StubContentProvider$C33"
1058-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1197:13-80
1059            android:authorities="${applicationId}.virtual_stub_33"
1059-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1198:13-67
1060            android:exported="false"
1060-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1199:13-37
1061            android:process=":p33" />
1061-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1200:13-35
1062        <provider
1062-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1202:9-1206:38
1063            android:name="com.lody.virtual.client.stub.StubContentProvider$C34"
1063-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1203:13-80
1064            android:authorities="${applicationId}.virtual_stub_34"
1064-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1204:13-67
1065            android:exported="false"
1065-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1205:13-37
1066            android:process=":p34" />
1066-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1206:13-35
1067        <provider
1067-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1208:9-1212:38
1068            android:name="com.lody.virtual.client.stub.StubContentProvider$C35"
1068-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1209:13-80
1069            android:authorities="${applicationId}.virtual_stub_35"
1069-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1210:13-67
1070            android:exported="false"
1070-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1211:13-37
1071            android:process=":p35" />
1071-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1212:13-35
1072        <provider
1072-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1214:9-1218:38
1073            android:name="com.lody.virtual.client.stub.StubContentProvider$C36"
1073-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1215:13-80
1074            android:authorities="${applicationId}.virtual_stub_36"
1074-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1216:13-67
1075            android:exported="false"
1075-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1217:13-37
1076            android:process=":p36" />
1076-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1218:13-35
1077        <provider
1077-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1220:9-1224:38
1078            android:name="com.lody.virtual.client.stub.StubContentProvider$C37"
1078-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1221:13-80
1079            android:authorities="${applicationId}.virtual_stub_37"
1079-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1222:13-67
1080            android:exported="false"
1080-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1223:13-37
1081            android:process=":p37" />
1081-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1224:13-35
1082        <provider
1082-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1226:9-1230:38
1083            android:name="com.lody.virtual.client.stub.StubContentProvider$C38"
1083-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1227:13-80
1084            android:authorities="${applicationId}.virtual_stub_38"
1084-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1228:13-67
1085            android:exported="false"
1085-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1229:13-37
1086            android:process=":p38" />
1086-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1230:13-35
1087        <provider
1087-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1232:9-1236:38
1088            android:name="com.lody.virtual.client.stub.StubContentProvider$C39"
1088-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1233:13-80
1089            android:authorities="${applicationId}.virtual_stub_39"
1089-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1234:13-67
1090            android:exported="false"
1090-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1235:13-37
1091            android:process=":p39" />
1091-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1236:13-35
1092        <provider
1092-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1238:9-1242:38
1093            android:name="com.lody.virtual.client.stub.StubContentProvider$C40"
1093-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1239:13-80
1094            android:authorities="${applicationId}.virtual_stub_40"
1094-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1240:13-67
1095            android:exported="false"
1095-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1241:13-37
1096            android:process=":p40" />
1096-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1242:13-35
1097        <provider
1097-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1244:9-1248:38
1098            android:name="com.lody.virtual.client.stub.StubContentProvider$C41"
1098-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1245:13-80
1099            android:authorities="${applicationId}.virtual_stub_41"
1099-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1246:13-67
1100            android:exported="false"
1100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1247:13-37
1101            android:process=":p41" />
1101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1248:13-35
1102        <provider
1102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1250:9-1254:38
1103            android:name="com.lody.virtual.client.stub.StubContentProvider$C42"
1103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1251:13-80
1104            android:authorities="${applicationId}.virtual_stub_42"
1104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1252:13-67
1105            android:exported="false"
1105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1253:13-37
1106            android:process=":p42" />
1106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1254:13-35
1107        <provider
1107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1256:9-1260:38
1108            android:name="com.lody.virtual.client.stub.StubContentProvider$C43"
1108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1257:13-80
1109            android:authorities="${applicationId}.virtual_stub_43"
1109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1258:13-67
1110            android:exported="false"
1110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1259:13-37
1111            android:process=":p43" />
1111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1260:13-35
1112        <provider
1112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1262:9-1266:38
1113            android:name="com.lody.virtual.client.stub.StubContentProvider$C44"
1113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1263:13-80
1114            android:authorities="${applicationId}.virtual_stub_44"
1114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1264:13-67
1115            android:exported="false"
1115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1265:13-37
1116            android:process=":p44" />
1116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1266:13-35
1117        <provider
1117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1268:9-1272:38
1118            android:name="com.lody.virtual.client.stub.StubContentProvider$C45"
1118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1269:13-80
1119            android:authorities="${applicationId}.virtual_stub_45"
1119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1270:13-67
1120            android:exported="false"
1120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1271:13-37
1121            android:process=":p45" />
1121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1272:13-35
1122        <provider
1122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1274:9-1278:38
1123            android:name="com.lody.virtual.client.stub.StubContentProvider$C46"
1123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1275:13-80
1124            android:authorities="${applicationId}.virtual_stub_46"
1124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1276:13-67
1125            android:exported="false"
1125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1277:13-37
1126            android:process=":p46" />
1126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1278:13-35
1127        <provider
1127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1280:9-1284:38
1128            android:name="com.lody.virtual.client.stub.StubContentProvider$C47"
1128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1281:13-80
1129            android:authorities="${applicationId}.virtual_stub_47"
1129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1282:13-67
1130            android:exported="false"
1130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1283:13-37
1131            android:process=":p47" />
1131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1284:13-35
1132        <provider
1132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1286:9-1290:38
1133            android:name="com.lody.virtual.client.stub.StubContentProvider$C48"
1133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1287:13-80
1134            android:authorities="${applicationId}.virtual_stub_48"
1134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1288:13-67
1135            android:exported="false"
1135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1289:13-37
1136            android:process=":p48" />
1136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1290:13-35
1137        <provider
1137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1292:9-1296:38
1138            android:name="com.lody.virtual.client.stub.StubContentProvider$C49"
1138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1293:13-80
1139            android:authorities="${applicationId}.virtual_stub_49"
1139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1294:13-67
1140            android:exported="false"
1140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1295:13-37
1141            android:process=":p49" />
1141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1296:13-35
1142    </application>
1143
1144</manifest>
