/*
 * Copyright (c) 2016-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#pragma once

#include "CoreClasses.h"

namespace facebook {
namespace jni {

class JFile : public JavaClass<JFile> {
 public:
  static constexpr const char* kJavaDescriptor = "Ljava/io/File;";

  // Define a method that calls into the represented Java class
  std::string getAbsolutePath() {
    static auto method = getClass()->getMethod<jstring()>("getAbsolutePath");
    return method(self())->toStdString();
  }

};

}
}
