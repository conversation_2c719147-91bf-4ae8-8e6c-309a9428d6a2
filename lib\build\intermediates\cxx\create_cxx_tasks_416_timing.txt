# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 33ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 13ms
    [gap of 14ms]
    create-variant-model 24ms
    [gap of 10ms]
  create-initial-cxx-model completed in 63ms
create_cxx_tasks completed in 64ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 35ms
create_cxx_tasks completed in 38ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 30ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 41ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 37ms
create_cxx_tasks completed in 46ms

