@echo off
"D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd" ^
  "NDK_PROJECT_PATH=null" ^
  "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk" ^
  "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk" ^
  "APP_ABI=arm64-v8a" ^
  "NDK_ALL_ABIS=arm64-v8a" ^
  "NDK_DEBUG=1" ^
  "APP_PLATFORM=android-21" ^
  "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj" ^
  "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib" ^
  "APP_SHORT_COMMANDS=false" ^
  "LOCAL_SHORT_COMMANDS=false" ^
  -B ^
  -n
