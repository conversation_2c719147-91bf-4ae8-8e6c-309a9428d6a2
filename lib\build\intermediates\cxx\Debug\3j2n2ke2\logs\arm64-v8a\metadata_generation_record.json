[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx\\Debug\\3j2n2ke2\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx\\Debug\\3j2n2ke2\\arm64-v8a'", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx\\Debug\\3j2n2ke2\\arm64-v8a'", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing ndkBuild @echo off\n\"D:\\\\test\\\\ndk\\\\25.1.8937393\\\\ndk-build.cmd\" ^\n  \"NDK_PROJECT_PATH=null\" ^\n  \"APP_BUILD_SCRIPT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\src\\\\main\\\\jni\\\\Android.mk\" ^\n  \"NDK_APPLICATION_MK=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\src\\\\main\\\\jni\\\\Application.mk\" ^\n  \"APP_ABI=arm64-v8a\" ^\n  \"NDK_ALL_ABIS=arm64-v8a\" ^\n  \"NDK_DEBUG=1\" ^\n  \"APP_PLATFORM=android-21\" ^\n  \"NDK_OUT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3j2n2ke2/obj\" ^\n  \"NDK_LIBS_OUT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3j2n2ke2/lib\" ^\n  \"APP_SHORT_COMMANDS=false\" ^\n  \"LOCAL_SHORT_COMMANDS=false\" ^\n  -B ^\n  -n\n", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\test\\\\ndk\\\\25.1.8937393\\\\ndk-build.cmd\" ^\n  \"NDK_PROJECT_PATH=null\" ^\n  \"APP_BUILD_SCRIPT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\src\\\\main\\\\jni\\\\Android.mk\" ^\n  \"NDK_APPLICATION_MK=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\src\\\\main\\\\jni\\\\Application.mk\" ^\n  \"APP_ABI=arm64-v8a\" ^\n  \"NDK_ALL_ABIS=arm64-v8a\" ^\n  \"NDK_DEBUG=1\" ^\n  \"APP_PLATFORM=android-21\" ^\n  \"NDK_OUT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3j2n2ke2/obj\" ^\n  \"NDK_LIBS_OUT=D:\\\\ypb_xp\\\\VirtualApp-master\\\\VirtualApp-master\\\\VirtualApp\\\\lib\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\3j2n2ke2/lib\" ^\n  \"APP_SHORT_COMMANDS=false\" ^\n  \"LOCAL_SHORT_COMMANDS=false\" ^\n  -B ^\n  -n\n", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "parse and convert ndk-build output to build configuration JSON", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "found application make file D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing ndkBuild", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]