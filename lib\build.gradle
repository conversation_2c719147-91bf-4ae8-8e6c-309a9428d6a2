apply plugin: 'com.android.library'

android {
    compileSdkVersion 26
    buildToolsVersion '26.0.2'

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 22
        versionCode 1
        versionName "1.0"
        externalNativeBuild {
            ndkBuild {
                abiFilters "armeabi-v7a", "x86"
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file("src/main/jni/Android.mk")
        }
    }
    lintOptions {
        //IJobService need NewApi
        warning 'NewApi','OnClick'
    }
}


dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
}
