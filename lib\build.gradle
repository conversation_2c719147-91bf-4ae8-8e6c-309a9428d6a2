plugins {
    id 'com.android.library'
}

android {
    namespace 'io.virtualapp.lib'
    compileSdk 34

    defaultConfig {
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        externalNativeBuild {
            ndkBuild {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file("src/main/jni/Android.mk")
        }
    }
    lintOptions {
        //IJobService need NewApi
        warning 'NewApi','OnClick'
    }
}


dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
}
