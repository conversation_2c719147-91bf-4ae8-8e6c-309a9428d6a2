/**
 *    Copyright 2017 jmpews
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

#ifndef zzdeps_posix_thread_utils_h
#define zzdeps_posix_thread_utils_h

#include <err.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>

#include <pthread.h>

#include "../zz.h"

typedef struct _ThreadLocalKey {
    pthread_key_t key;
} ThreadLocalKey;

typedef struct _ThreadLocalKeyList {
    zsize size;
    zsize capacity;
    ThreadLocalKey **keys;
} ThreadLocalKeyList;

void zz_posix_thread_initialize_thread_local_key_list();

zpointer zz_posix_thread_new_thread_local_key_ptr();

zpointer zz_posix_thread_get_current_thread_data(zpointer key_ptr);

zbool zz_posix_thread_set_current_thread_data(zpointer key_ptr, zpointer data);

long zz_posix_get_current_thread_id();

#endif