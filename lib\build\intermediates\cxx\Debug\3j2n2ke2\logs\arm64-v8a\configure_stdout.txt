Android NDK: WARNING: Ignoring unknown import directory: :D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    
Android NDK: WARNING:D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/Android.mk:fb: LOCAL_LDLIBS is always ignored for static libraries    
Android NDK: WARNING:D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\jni\Android.mk:va++: non-system libraries in linker flags: -latomic    
Android NDK:     This is likely to result in incorrect builds. Try using LOCAL_STATIC_LIBRARIES    
Android NDK:     or LOCAL_SHARED_LIBRARIES instead to list the library dependencies of the    
Android NDK:     current module    
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\va++\Jni" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= VAJni.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Jni/VAJni.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni/VAJni.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Jni/VAJni.o
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\va++\Foundation" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= IOUniformer.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/IOUniformer.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/IOUniformer.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= VMPatch.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/VMPatch.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/VMPatch.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/VMPatch.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= SymbolFinder.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SymbolFinder.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/SymbolFinder.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SymbolFinder.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= Path.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/Path.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/Path.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/Path.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= SandboxFs.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SandboxFs.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/SandboxFs.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SandboxFs.o
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\va++\Substrate" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile        ": "va++ <= hde64.c"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/hde64.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni   -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/hde64.c -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/hde64.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= SubstrateDebug.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateDebug.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateDebug.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateDebug.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= SubstrateHook.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateHook.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateHook.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateHook.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "va++ <= SubstratePosixMemory.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstratePosixMemory.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    -DANDROID -Wno-error=format-security -fpermissive -DLOG_TAG=\"VA++\" -fno-rtti -fno-exceptions -nostdinc++ -Wformat -Werror=format-security -std=gnu++11 -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstratePosixMemory.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstratePosixMemory.o
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a" >NUL 2>NUL || rem
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\fb" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= assert.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/assert.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/assert.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/assert.o
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\fb\jni" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= ByteBuffer.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/ByteBuffer.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/ByteBuffer.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/ByteBuffer.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= Countable.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Countable.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/Countable.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Countable.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= Environment.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Environment.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/Environment.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Environment.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= Exceptions.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Exceptions.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/Exceptions.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Exceptions.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= fbjni.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/fbjni.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/fbjni.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/fbjni.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= Hybrid.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Hybrid.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/Hybrid.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Hybrid.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= jni_helpers.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/jni_helpers.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/jni_helpers.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/jni_helpers.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= LocalString.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/LocalString.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/LocalString.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/LocalString.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= OnLoad.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/OnLoad.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/OnLoad.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/OnLoad.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= References.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/References.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/References.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/References.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= WeakReference.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/WeakReference.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/jni/WeakReference.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/WeakReference.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= log.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/log.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/log.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/log.o
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\objs\fb\lyra" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= lyra.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/lyra/lyra.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/lyra/lyra.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/lyra/lyra.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "fb <= onload.cpp"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/onload.o.d -target aarch64-none-linux-android21 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O2 -DNDEBUG  -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include -ID:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++abi/include -ID:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb    -DANDROID -DLOG_TAG=\"libfb\" -DDISABLE_CPUCAP -DDISABLE_XPLAT -fexceptions -frtti -Wall -Werror -Wno-unused-parameter -DHAVE_POSIX_CLOCKS -std=gnu++11 -nostdinc++ -Wformat -Werror=format-security -fno-strict-aliasing -frtti -fexceptions  -c  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/onload.cpp -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/onload.o
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "StaticLibrary  ": "libfb.a"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-ar.exe crsD  D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/libfb.a D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/assert.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/ByteBuffer.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Countable.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Environment.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Exceptions.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/fbjni.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/Hybrid.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/jni_helpers.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/LocalString.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/OnLoad.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/References.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/jni/WeakReference.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/log.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/lyra/lyra.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/onload.o
md "D:\test\ndk\25.1.8937393\build\..\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\lib\aarch64-linux-android" >NUL 2>NUL || rem
md "D:\test\ndk\25.1.8937393\toolchains\llvm\prebuilt\windows-x86_64\lib64\clang\14.0.6\lib\linux\aarch64" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "SharedLibrary  ": "libva++.so"
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -Wl,--gc-sections -Wl,-soname,libva++.so -shared D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Jni/VAJni.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/IOUniformer.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/VMPatch.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SymbolFinder.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/Path.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SandboxFs.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/hde64.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateDebug.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstrateHook.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Substrate/SubstratePosixMemory.o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/libfb.a D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_static.a D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++abi.a D:/test/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/14.0.6/lib/linux/aarch64/libunwind.a -latomic -target aarch64-none-linux-android21 -no-canonical-prefixes   -Wl,--build-id=sha1 -Wl,--no-rosegment  -nostdlib++ -Wl,--no-undefined -Wl,--fatal-warnings -llog -latomic -llog -lc -lm -o D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/libva++.so
md "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\lib\arm64-v8a" >NUL 2>NUL || rem
D:/test/ndk/25.1.8937393/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Install        ": "libva++.so => D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/lib/arm64-v8a/libva++.so"
copy /b/y "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\obj\local\arm64-v8a\libva++.so" "D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2\lib\arm64-v8a\libva++.so" > NUL
D:/test/ndk/25.1.8937393/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe --strip-unneeded  D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/lib/arm64-v8a/libva++.so
