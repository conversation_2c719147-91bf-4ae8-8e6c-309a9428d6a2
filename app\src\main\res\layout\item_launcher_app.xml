<?xml version="1.0" encoding="utf-8"?>
<android.support.v7.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    app:cardElevation="10dp">

    <io.virtualapp.widgets.LabelView
        xmlns:lv="http://schemas.android.com/apk/res-auto"
        android:id="@+id/item_app_space_idx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:visibility="invisible"
        lv:lv_background_color="#3F9FE0"
        lv:lv_gravity="TOP_RIGHT"
        lv:lv_text="2"
        lv:lv_text_size="12sp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingBottom="35dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingTop="35dp">

        <io.virtualapp.widgets.LauncherIconView
            android:id="@+id/item_app_icon"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_marginBottom="12dp"
            app:pi_mask_color="#CC233333"
            app:pi_progress="0"
            app:pi_radius="40dp"
            app:pi_stroke="6dp" />


        <io.virtualapp.widgets.MarqueeTextView
            android:id="@+id/item_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="#ffffff"
            android:textSize="12sp" />

        <View
            android:id="@+id/item_first_open_dot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/blue_circle"
            android:visibility="invisible" />

    </LinearLayout>

</android.support.v7.widget.CardView>