D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:129:23: error: use of undeclared identifier '__NR_chmod'
    int ret = syscall(__NR_chmod, redirect_path, mode);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:139:23: error: use of undeclared identifier '__NR_fstatat64'
    int ret = syscall(__NR_fstatat64, dirfd, redirect_path, buf, flags);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:148:23: error: use of undeclared identifier '__NR_fstatat64'
    int ret = syscall(__NR_fstatat64, dirfd, redirect_path, buf, flags);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:158:23: error: use of undeclared identifier '__NR_fstat64'
    int ret = syscall(__NR_fstat64, redirect_path, buf);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:176:23: error: use of undeclared identifier '__NR_mknod'
    int ret = syscall(__NR_mknod, redirect_path, mode, dev);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:229:23: error: use of undeclared identifier '__NR_rename'
    int ret = syscall(__NR_rename, redirect_path_old, redirect_path_new);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:248:23: error: use of undeclared identifier '__NR_unlink'
    int ret = syscall(__NR_unlink, redirect_path);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:271:23: error: use of undeclared identifier '__NR_symlink'
    int ret = syscall(__NR_symlink, redirect_path_old, redirect_path_new);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:296:23: error: use of undeclared identifier '__NR_link'
    int ret = syscall(__NR_link, redirect_path_old, redirect_path_new);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:307:23: error: use of undeclared identifier '__NR_utimes'
    int ret = syscall(__NR_utimes, redirect_path, tvp);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:317:23: error: use of undeclared identifier '__NR_access'
    int ret = syscall(__NR_access, redirect_path, mode);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:327:23: error: use of undeclared identifier '__NR_chmod'
    int ret = syscall(__NR_chmod, redirect_path, mode);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:337:23: error: use of undeclared identifier '__NR_chown'
    int ret = syscall(__NR_chown, redirect_path, owner, group);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:347:23: error: use of undeclared identifier '__NR_lstat64'
    int ret = syscall(__NR_lstat64, redirect_path, buf);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:357:23: error: use of undeclared identifier '__NR_stat64'
    int ret = syscall(__NR_stat64, redirect_path, buf);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:375:23: error: use of undeclared identifier '__NR_mkdir'
    int ret = syscall(__NR_mkdir, redirect_path, mode);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:385:23: error: use of undeclared identifier '__NR_rmdir'
    int ret = syscall(__NR_rmdir, redirect_path);
                      ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:403:27: error: use of undeclared identifier '__NR_readlink'
    ssize_t ret = syscall(__NR_readlink, redirect_path, buf, bufsiz);
                          ^
D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/IOUniformer.cpp:413:23: error: use of undeclared identifier '__NR_statfs64'
    int ret = syscall(__NR_statfs64, redirect_path, size, stat);
                      ^
fatal error: too many errors emitted, stopping now [-ferror-limit=]
20 errors generated.
make: *** [D:/test/ndk/25.1.8937393/build/../build/core/build-binary.mk:424: D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/IOUniformer.o] Error 1
