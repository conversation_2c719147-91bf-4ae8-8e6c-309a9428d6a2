<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res"><file name="app_not_authorized" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\app_not_authorized.xml" qualifiers="" type="layout"/><file name="choose_account_row" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml" qualifiers="" type="layout"/><file name="choose_account_type" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_type.xml" qualifiers="" type="layout"/><file name="choose_type_and_account" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_type_and_account.xml" qualifiers="" type="layout"/><file name="custom_notification" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml" qualifiers="" type="layout"/><file name="custom_notification_lite" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification_lite.xml" qualifiers="" type="layout"/><file name="resolve_list_item" path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml" qualifiers="" type="layout"/><file path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\values\dimens.xml" qualifiers=""><dimen name="match_parent">-1px</dimen><dimen name="standard_notification_panel_width">416dp
    </dimen><dimen name="notification_min_height">64dp</dimen><dimen name="notification_side_padding">8dp</dimen><dimen name="notification_panel_width">-1dp</dimen><dimen name="notification_max_height">256dp</dimen><dimen name="notification_padding">4dp</dimen><dimen name="notification_mid_height">128dp</dimen></file><file path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\values\integer.xml" qualifiers=""><integer name="config_maxResolverActivityColumns">8</integer></file><file path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\values\strings.xml" qualifiers=""><string name="engine_process_name">:x</string><string name="virtual_installer">VirtualPackage Installer</string><string name="owner_name">Admin</string><string name="choose">Choose</string><string name="choose_empty">Chooser is Empty</string><string name="noApplications">No find applications</string><string name="add_account_button_label">Add account</string></file><file path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\values\styles.xml" qualifiers=""><style name="notification_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">1</item>
    </style><style name="notification_button">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_weight">1</item>
    </style><style name="VATheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowDisablePreview">true</item>
    </style><style name="VAAlertTheme" parent="android:Theme.DeviceDefault.Dialog">
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>