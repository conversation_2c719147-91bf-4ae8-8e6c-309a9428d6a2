package mirror.android.content;

import android.content.SyncAdapterType;

import mirror.MethodParams;
import mirror.RefClass;
import mirror.RefConstructor;

public class SyncAdapterTypeN {
    public static Class<?> Class = RefClass.load(SyncAdapterTypeN.class, SyncAdapterType.class);
    @MethodParams({String.class, String.class, boolean.class, boolean.class, boolean.class, boolean.class, String.class, String.class})
    public static RefConstructor<SyncAdapterType> ctor;
}