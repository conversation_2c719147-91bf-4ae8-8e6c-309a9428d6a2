plugins {
    id 'com.android.application'
}

android {
    namespace 'io.virtualapp'
    compileSdk 34

    defaultConfig {
        applicationId "io.virtualapp"
        minSdk 21
        targetSdk 34
        versionCode 24
        versionName "1.2.5"
        multiDexEnabled true

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main{
            jniLibs.srcDirs = ['libs']
        }
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

android {
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
    compile project(':lib')
    //Android Lib
    compile 'com.android.support:multidex:1.0.2'
    compile 'com.android.support:appcompat-v7:25.4.0'
    compile 'com.melnykov:floatingactionbutton:1.3.0'
    compile 'com.android.support:recyclerview-v7:25.4.0'
    compile 'com.android.support:percent:25.4.0'
    compile 'com.android.support:design:25.4.0'
    compile 'com.android.support:cardview-v7:25.4.0'
    //Promise Support
    compile 'org.jdeferred:jdeferred-android-aar:1.2.4'
    // ThirdParty
    compile 'com.jonathanfinerty.once:once:1.0.3'
    compile 'com.flurry.android:analytics:6.9.2'
    compile 'com.kyleduo.switchbutton:library:1.4.6'
}
