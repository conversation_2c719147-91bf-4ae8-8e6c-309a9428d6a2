plugins {
    id 'com.android.application'
}

android {
    namespace 'io.virtualapp'
    compileSdk 34

    defaultConfig {
        applicationId "io.virtualapp"
        minSdk 21
        targetSdk 34
        versionCode 24
        versionName "1.2.5"
        multiDexEnabled true

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main{
            jniLibs.srcDirs = ['libs']
        }
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

android {
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':lib')
    //Android Lib - 迁移到 AndroidX
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.melnykov:floatingactionbutton:1.3.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.percentlayout:percentlayout:1.0.0'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    //Promise Support
    implementation 'org.jdeferred:jdeferred-android-aar:1.2.4'
    // ThirdParty
    implementation 'com.jonathanfinerty.once:once:1.0.3'
    implementation 'com.flurry.android:analytics:6.9.2'
    implementation 'com.kyleduo.switchbutton:library:1.4.6'
}
