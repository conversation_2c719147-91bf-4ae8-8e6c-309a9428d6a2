{"buildFiles": ["D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk"], "cleanCommandsComponents": [["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=x86", "NDK_ALL_ABIS=x86", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "clean"]], "buildTargetsCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=x86", "NDK_ALL_ABIS=x86", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"fb-debug-x86": {"buildCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=x86", "NDK_ALL_ABIS=x86", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj/local/x86/libfb.a"], "toolchain": "toolchain-x86", "abi": "x86", "artifactName": "fb", "output": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2\\obj\\local\\x86\\libfb.a"}, "va++-debug-x86": {"buildCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=x86", "NDK_ALL_ABIS=x86", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj/local/x86/libva++.so"], "toolchain": "toolchain-x86", "abi": "x86", "artifactName": "va++", "output": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2\\obj\\local\\x86\\libva++.so"}}, "toolchains": {"toolchain-x86": {"cCompilerExecutable": "D:\\test\\ndk\\25.1.8937393\\build\\..\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\test\\ndk\\25.1.8937393\\build\\..\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": ["c"], "cppFileExtensions": ["cpp"]}