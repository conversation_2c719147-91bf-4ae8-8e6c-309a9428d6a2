<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="true"
    android:fitsSystemWindows="true"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/content_toolbar"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <io.virtualapp.widgets.fittext.FitTextView
                android:id="@+id/address"
                android:layout_width="match_parent"
                android:layout_height="@dimen/line_height"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:gravity="center_vertical"
                android:maxLines="2"
                app:ftMaxTextSize="20sp"
                app:ftMinTextSize="10sp"
                tools:text="hello"/>

            <com.tencent.tencentmap.mapsdk.map.MapView
                android:id="@+id/map"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent" />
        </LinearLayout>
    </LinearLayout>
</android.support.design.widget.CoordinatorLayout>