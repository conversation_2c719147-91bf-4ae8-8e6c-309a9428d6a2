io.virtualapp-cardview-1.0.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\04982dfa78b345590af07a01319f4e04\transformed\cardview-1.0.0\res
io.virtualapp-core-1.9.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\res
io.virtualapp-core-runtime-2.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\0f7a997963371c33c0d9d42e356b95ab\transformed\core-runtime-2.2.0\res
io.virtualapp-jetified-viewpager2-1.1.0-beta02-3 C:\Users\<USER>\.gradle\caches\transforms-3\0f831f154b9005859090fa2019cda539\transformed\jetified-viewpager2-1.1.0-beta02\res
io.virtualapp-jetified-core-ktx-1.9.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\113bb9539d87fc3bedb001cef1bd98f7\transformed\jetified-core-ktx-1.9.0\res
io.virtualapp-constraintlayout-2.0.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\181d1cc96c37340295cde1cf06f38d13\transformed\constraintlayout-2.0.1\res
io.virtualapp-recyclerview-1.3.2-6 C:\Users\<USER>\.gradle\caches\transforms-3\23fbc55d1a0c197075fe85502f7d41db\transformed\recyclerview-1.3.2\res
io.virtualapp-appcompat-1.6.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\27f944d5dd141e8c451c2518b7151c40\transformed\appcompat-1.6.1\res
io.virtualapp-material-1.11.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\323f159c69224d5e1640dba887d53aae\transformed\material-1.11.0\res
io.virtualapp-jetified-appcompat-resources-1.6.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\3c7d53b138251e52520fa678390d14d3\transformed\jetified-appcompat-resources-1.6.1\res
io.virtualapp-lifecycle-runtime-2.6.1-10 C:\Users\<USER>\.gradle\caches\transforms-3\49dae7731ea8d2e57a7c44f7571970e4\transformed\lifecycle-runtime-2.6.1\res
io.virtualapp-transition-1.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\5862a699a5a5e69d26ba1ea3c76fc3c9\transformed\transition-1.2.0\res
io.virtualapp-jetified-customview-poolingcontainer-1.0.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\58b231ac675cf6337b06684857d999d3\transformed\jetified-customview-poolingcontainer-1.0.0\res
io.virtualapp-jetified-lifecycle-process-2.6.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\res
io.virtualapp-jetified-emoji2-1.2.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\res
io.virtualapp-lifecycle-viewmodel-2.6.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\62d69820d9a17d65d0ba68952919f471\transformed\lifecycle-viewmodel-2.6.1\res
io.virtualapp-lifecycle-livedata-core-2.6.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\6552558ac927405459e4c25f04f0ce46\transformed\lifecycle-livedata-core-2.6.1\res
io.virtualapp-jetified-savedstate-1.2.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\712d77af3cad31e4545eee5d86cbeb9c\transformed\jetified-savedstate-1.2.1\res
io.virtualapp-jetified-activity-1.8.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\7f9cf7a6d4b3333cfa1dba4cbe1911b9\transformed\jetified-activity-1.8.0\res
io.virtualapp-jetified-lifecycle-viewmodel-savedstate-2.6.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\8120d0ac8c79014d8e5539f557c5be1e\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\res
io.virtualapp-percentlayout-1.0.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\81868c48d37ca09ca09c88c742906fd7\transformed\percentlayout-1.0.0\res
io.virtualapp-jetified-jdeferred-android-aar-1.2.4-21 C:\Users\<USER>\.gradle\caches\transforms-3\86c22f2c98b9cdfcce3b605ec68af72a\transformed\jetified-jdeferred-android-aar-1.2.4\res
io.virtualapp-jetified-floatingactionbutton-1.3.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\8d5559e1caa9a18d2fd1a35d40b408ca\transformed\jetified-floatingactionbutton-1.3.0\res
io.virtualapp-jetified-annotation-experimental-1.3.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\92a3569edc4ae9c3a29a51ed4ec4f244\transformed\jetified-annotation-experimental-1.3.0\res
io.virtualapp-drawerlayout-1.1.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\acb93310fddccd383c579526aea6dc7a\transformed\drawerlayout-1.1.1\res
io.virtualapp-jetified-startup-runtime-1.1.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\ae5cffb9d30a2e394ede675df36b2642\transformed\jetified-startup-runtime-1.1.1\res
io.virtualapp-fragment-1.3.6-26 C:\Users\<USER>\.gradle\caches\transforms-3\b4d6351b0ffb103a9b1cd1c2bcdce657\transformed\fragment-1.3.6\res
io.virtualapp-coordinatorlayout-1.1.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\c644e30b2e2c41e24dcfd3d40d996371\transformed\coordinatorlayout-1.1.0\res
io.virtualapp-lifecycle-livedata-2.6.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\cbde9d094f5b3e6e3e46f02c42fe631c\transformed\lifecycle-livedata-2.6.1\res
io.virtualapp-jetified-profileinstaller-1.3.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\res
io.virtualapp-jetified-emoji2-views-helper-1.2.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\f6795b0d6835212b597a04a913d30111\transformed\jetified-emoji2-views-helper-1.2.0\res
io.virtualapp-pngs-31 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\build\generated\res\pngs\debug
io.virtualapp-resValues-32 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\build\generated\res\resValues\debug
io.virtualapp-packageDebugResources-33 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
io.virtualapp-packageDebugResources-34 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
io.virtualapp-merged_res-35 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\build\intermediates\merged_res\debug
io.virtualapp-debug-36 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\debug\res
io.virtualapp-main-37 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\res
io.virtualapp-packaged_res-38 D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\packaged_res\debug
