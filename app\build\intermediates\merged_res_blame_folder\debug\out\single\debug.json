[{"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_add_circle.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_add_circle.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_shortcut.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_shortcut.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_crash.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_crash.png"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_users.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_users.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_loading.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_loading.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable_sel_clone_app_btn.xml.flat", "source": "io.virtualapp-main-37:/drawable/sel_clone_app_btn.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_item_location_app.xml.flat", "source": "io.virtualapp-main-37:/layout/item_location_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_menu.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_menu.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable_shape_clone_app_btn.xml.flat", "source": "io.virtualapp-main-37:/drawable/shape_clone_app_btn.xml"}, {"merged": "io.virtualapp-merged_res-35:/menu_marktet_map.xml.flat", "source": "io.virtualapp-main-37:/menu/marktet_map.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable_shape_clone_app_btn_pressed.xml.flat", "source": "io.virtualapp-main-37:/drawable/shape_clone_app_btn_pressed.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_item_app.xml.flat", "source": "io.virtualapp-main-37:/layout/item_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable_sel_guide_btn.xml.flat", "source": "io.virtualapp-main-37:/drawable/sel_guide_btn.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_item_clone_app.xml.flat", "source": "io.virtualapp-main-37:/layout/item_clone_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/menu_user_menu.xml.flat", "source": "io.virtualapp-main-37:/menu/user_menu.xml"}, {"merged": "io.virtualapp-merged_res-35:/mipmap-hdpi_ic_launcher.png.flat", "source": "io.virtualapp-main-37:/mipmap-hdpi/ic_launcher.png"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_splash.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_splash.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_wifi.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_wifi.png"}, {"merged": "io.virtualapp-merged_res-35:/layout_item_launcher_app.xml.flat", "source": "io.virtualapp-main-37:/layout/item_launcher_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_notification.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_notification.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_no_check.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_no_check.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_settings.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_settings.png"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_clone_app.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_clone_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_check.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_check.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_about.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_about.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable_blue_circle.xml.flat", "source": "io.virtualapp-main-37:/drawable/blue_circle.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_item_user.xml.flat", "source": "io.virtualapp-main-37:/layout/item_user.xml"}, {"merged": "io.virtualapp-merged_res-35:/mipmap-xhdpi_ic_launcher.png.flat", "source": "io.virtualapp-main-37:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_account.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_account.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable_icon_bg.xml.flat", "source": "io.virtualapp-main-37:/drawable/icon_bg.xml"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_device.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_device.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-hdpi_ic_add.png.flat", "source": "io.virtualapp-main-37:/drawable-hdpi/ic_add.png"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_install.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_install.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_home.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_home.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_fragment_list_app.xml.flat", "source": "io.virtualapp-main-37:/layout/fragment_list_app.xml"}, {"merged": "io.virtualapp-merged_res-35:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "io.virtualapp-main-37:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable_fab_bg.xml.flat", "source": "io.virtualapp-main-37:/drawable/fab_bg.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_marker.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_marker.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_activity_location_settings.xml.flat", "source": "io.virtualapp-main-37:/layout/activity_location_settings.xml"}, {"merged": "io.virtualapp-merged_res-35:/mipmap-mdpi_ic_launcher.png.flat", "source": "io.virtualapp-main-37:/mipmap-mdpi/ic_launcher.png"}, {"merged": "io.virtualapp-merged_res-35:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "io.virtualapp-main-37:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable-xxhdpi_ic_vs.png.flat", "source": "io.virtualapp-main-37:/drawable-xxhdpi/ic_vs.png"}, {"merged": "io.virtualapp-merged_res-35:/drawable_home_bg.xml.flat", "source": "io.virtualapp-main-37:/drawable/home_bg.xml"}, {"merged": "io.virtualapp-merged_res-35:/layout_content_toolbar.xml.flat", "source": "io.virtualapp-main-37:/layout/content_toolbar.xml"}]