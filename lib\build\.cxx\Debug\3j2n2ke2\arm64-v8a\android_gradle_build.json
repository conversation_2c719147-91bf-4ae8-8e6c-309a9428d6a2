{"buildFiles": ["D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk"], "cleanCommandsComponents": [["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "clean"]], "buildTargetsCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"va++-debug-arm64-v8a": {"buildCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj/local/arm64-v8a/libva++.so"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "va++", "output": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2\\obj\\local\\arm64-v8a\\libva++.so"}, "fb-debug-arm64-v8a": {"buildCommandComponents": ["D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd", "NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/lib", "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2/obj/local/arm64-v8a/libfb.a"], "toolchain": "toolchain-arm64-v8a", "abi": "arm64-v8a", "artifactName": "fb", "output": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Debug\\3j2n2ke2\\obj\\local\\arm64-v8a\\libfb.a"}}, "toolchains": {"toolchain-arm64-v8a": {"cCompilerExecutable": "D:\\test\\ndk\\25.1.8937393\\build\\..\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\test\\ndk\\25.1.8937393\\build\\..\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": ["c"], "cppFileExtensions": ["cpp"]}