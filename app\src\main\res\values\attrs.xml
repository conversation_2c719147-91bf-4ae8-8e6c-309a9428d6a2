<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CardStackLayout">
        <attr name="parallax_enabled" format="boolean" />
        <attr name="parallax_scale" format="integer" />
        <attr name="card_gap" format="dimension" />
        <attr name="card_gap_bottom" format="dimension" />
        <attr name="showInitAnimation" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ProgressImageView">
        <attr name="pi_progress" format="integer" />
        <attr name="pi_mask_color" format="color" />
        <attr name="pi_stroke" format="dimension" />
        <attr name="pi_radius" format="dimension" />
        <attr name="pi_force_square" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ShimmerView">
        <attr name="reflectionColor" format="color" />
    </declare-styleable>

    <declare-styleable name="DragSelectRecyclerView">
        <attr name="dsrv_autoScrollHotspotHeight" format="dimension" />
        <attr name="dsrv_autoScrollEnabled" format="boolean" />
        <attr name="dsrv_autoScrollHotspot_offsetTop" format="dimension" />
        <attr name="dsrv_autoScrollHotspot_offsetBottom" format="dimension" />
    </declare-styleable>

    <declare-styleable name="LoadingIndicatorView">
        <attr name="minWidth" format="dimension" />
        <attr name="maxWidth" format="dimension" />
        <attr name="minHeight" format="dimension" />
        <attr name="maxHeight" format="dimension" />
        <attr name="indicatorName" format="string" />
        <attr name="indicatorColor" format="color" />
    </declare-styleable>

    <declare-styleable name="RippleButton">
        <attr name="rippleColor" format="color" />
        <attr name="alphaFactor" format="float" />
        <attr name="hover" format="boolean" />
    </declare-styleable>

    <declare-styleable name="MaterialRippleLayout">
        <attr name="mrl_rippleColor" format="color" localization="suggested" />
        <attr name="mrl_rippleDimension" format="dimension" localization="suggested" />
        <attr name="mrl_rippleOverlay" format="boolean" localization="suggested" />
        <attr name="mrl_rippleAlpha" format="float" localization="suggested" />
        <attr name="mrl_rippleDuration" format="integer" localization="suggested" />
        <attr name="mrl_rippleFadeDuration" format="integer" localization="suggested" />
        <attr name="mrl_rippleHover" format="boolean" localization="suggested" />
        <attr name="mrl_rippleBackground" format="color" localization="suggested" />
        <attr name="mrl_rippleDelayClick" format="boolean" localization="suggested" />
        <attr name="mrl_ripplePersistent" format="boolean" localization="suggested" />
        <attr name="mrl_rippleInAdapter" format="boolean" localization="suggested" />
        <attr name="mrl_rippleRoundedCorners" format="dimension" localization="suggested" />
    </declare-styleable>

    <declare-styleable name="LabelView">
        <!-- 设置文字内容 -->
        <attr name="lv_text" format="string"/>
        <!-- 设置文字颜色,默认#ffffff -->
        <attr name="lv_text_color" format="color"/>
        <!-- 设置文字大小,默认11sp -->
        <attr name="lv_text_size" format="dimension"/>
        <!-- 设置文字是否支持加粗,默认true -->
        <attr name="lv_text_bold" format="boolean"/>
        <!-- 设置文字是否支持全部大写,默认true-->
        <attr name="lv_text_all_caps" format="boolean"/>
        <!-- 设置背景颜色,默认"#FF4081"-->
        <attr name="lv_background_color" format="color"/>
        <!-- 设置LabelView所在矩形最小宽高,默认mFillTriangle?35dp:50dp-->
        <attr name="lv_min_size" format="dimension"/>
        <!-- 设置文字上下padding,默认3.5dp,mFillTriangle为true时无效-->
        <attr name="lv_padding" format="dimension"/>
        <!-- 设置LabelView方向 -->
        <attr name="lv_gravity" format="enum">
            <enum name="TOP_LEFT" value="51"/>
            <enum name="TOP_RIGHT" value="53"/>
            <enum name="BOTTOM_LEFT" value="83"/>
            <enum name="BOTTOM_RIGHT" value="85"/>
        </attr>
        <!-- 设置是否填充三角区域,默认false -->
        <attr name="lv_fill_triangle" format="boolean"/>

    </declare-styleable>

</resources>