<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/desktopColorA">

    <ImageView
        android:id="@+id/item_app_checked"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:layout_gravity="top|end"
        android:src="@drawable/ic_no_check" />

    <io.virtualapp.widgets.LabelView xmlns:lv="http://schemas.android.com/apk/res-auto"
        android:id="@+id/item_app_clone_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:visibility="invisible"
        lv:lv_background_color="#F6CE59"
        lv:lv_fill_triangle="true"
        lv:lv_gravity="BOTTOM_LEFT"
        lv:lv_text="2"
        lv:lv_text_all_caps="false"
        lv:lv_text_color="@android:color/white"
        lv:lv_text_size="12sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingBottom="35dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingTop="35dp">

        <ImageView
            android:id="@+id/item_app_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginBottom="12dp" />

        <io.virtualapp.widgets.MarqueeTextView
            android:id="@+id/item_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="#ffffff"
            android:textSize="12sp" />

    </LinearLayout>

</FrameLayout>