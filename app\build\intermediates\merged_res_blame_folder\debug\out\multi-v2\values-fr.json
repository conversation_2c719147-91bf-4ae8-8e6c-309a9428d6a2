{"logs": [{"outputFile": "io.virtualapp-mergeDebugResources-33:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\27f944d5dd141e8c451c2518b7151c40\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,8723", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,8805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0b3d22ab099177b3472464321ad2c41c\\transformed\\core-1.9.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8810", "endColumns": "100", "endOffsets": "8906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\323f159c69224d5e1640dba887d53aae\\transformed\\material-1.11.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1110,1207,1290,1356,1458,1523,1598,1654,1733,1793,1847,1969,2028,2090,2144,2226,2361,2453,2537,2681,2760,2841,2982,3075,3154,3209,3260,3326,3406,3487,3590,3670,3743,3821,3894,3966,4078,4171,4243,4335,4427,4501,4585,4677,4734,4818,4884,4967,5054,5116,5180,5243,5321,5423,5527,5624,5728,5787,5842", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "278,358,439,522,631,726,824,954,1039,1105,1202,1285,1351,1453,1518,1593,1649,1728,1788,1842,1964,2023,2085,2139,2221,2356,2448,2532,2676,2755,2836,2977,3070,3149,3204,3255,3321,3401,3482,3585,3665,3738,3816,3889,3961,4073,4166,4238,4330,4422,4496,4580,4672,4729,4813,4879,4962,5049,5111,5175,5238,5316,5418,5522,5619,5723,5782,5837,5926"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,3523,3621,3751,3836,3902,3999,4082,4148,4250,4315,4390,4446,4525,4585,4639,4761,4820,4882,4936,5018,5153,5245,5329,5473,5552,5633,5774,5867,5946,6001,6052,6118,6198,6279,6382,6462,6535,6613,6686,6758,6870,6963,7035,7127,7219,7293,7377,7469,7526,7610,7676,7759,7846,7908,7972,8035,8113,8215,8319,8416,8520,8579,8634", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,79,80,82,108,94,97,129,84,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,83,143,78,80,140,92,78,54,50,65,79,80,102,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88", "endOffsets": "328,3150,3231,3314,3423,3518,3616,3746,3831,3897,3994,4077,4143,4245,4310,4385,4441,4520,4580,4634,4756,4815,4877,4931,5013,5148,5240,5324,5468,5547,5628,5769,5862,5941,5996,6047,6113,6193,6274,6377,6457,6530,6608,6681,6753,6865,6958,7030,7122,7214,7288,7372,7464,7521,7605,7671,7754,7841,7903,7967,8030,8108,8210,8314,8411,8515,8574,8629,8718"}}]}]}