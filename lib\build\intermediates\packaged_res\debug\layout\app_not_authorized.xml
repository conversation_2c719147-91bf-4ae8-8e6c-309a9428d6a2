<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Customizable description text -->
    <TextView android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:layout_gravity="start|center_vertical"
        android:paddingTop="16dip"
        android:paddingBottom="16dip"
        android:paddingStart="16dip"
        android:paddingEnd="16dip"
        android:text="Change not allowed"
    />

    <!-- Horizontal divider line -->
    <View android:layout_height="1dip"
        android:layout_width="match_parent"
        android:background="?android:attr/dividerHorizontal" />

    <!-- Alert dialog style buttons along the bottom. -->
    <LinearLayout android:id="@+id/button_bar"
        style="?android:attr/buttonBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:measureWithLargestChild="true">
        <Button android:id="@android:id/button1"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dp" android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@android:string/yes"
            android:onClick="onCancelButtonClicked" />
    </LinearLayout>
</LinearLayout>
