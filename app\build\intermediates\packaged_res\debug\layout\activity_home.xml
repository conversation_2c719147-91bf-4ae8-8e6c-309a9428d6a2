<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fab="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginLeft="20dp"
            android:layout_marginStart="20dp"
            android:gravity="start"
            android:orientation="horizontal"
            fab:layout_heightPercent="12%">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center|start"
                android:text="@string/app_name"
                android:textColor="@android:color/white"
                android:textSize="22sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center|end"
                android:orientation="horizontal"
                android:visibility="visible">

                <io.virtualapp.widgets.MaterialRippleLayout
                    android:layout_width="60dp"
                    android:layout_height="60dp">

                    <ImageView
                        android:id="@+id/home_menu"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="15dp"
                        android:src="@drawable/ic_menu" />
                </io.virtualapp.widgets.MaterialRippleLayout>

            </LinearLayout>

        </LinearLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <android.support.v7.widget.RecyclerView
                android:id="@+id/home_launcher"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:scrollbars="vertical"
                tools:listitem="@layout/item_launcher_app" />

            <io.virtualapp.widgets.TwoGearsView
                android:id="@+id/pb_loading_app"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_gravity="center"
                android:layout_marginBottom="30dp" />
        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/bottom_area"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="bottom|center"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:visibility="gone"
        android:weightSum="1">

        <LinearLayout
            android:id="@+id/create_shortcut_area"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.5"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:src="@drawable/ic_shortcut" />

            <TextView
                android:id="@+id/create_shortcut_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginStart="10dp"
                android:background="@color/colorPrimary"
                android:gravity="center|start"
                android:padding="5dp"
                android:text="@string/create_shortcut"
                android:textColor="@android:color/white" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/delete_app_area"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.5"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:src="@drawable/ic_crash" />

            <TextView
                android:id="@+id/delete_app_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginStart="10dp"
                android:background="@color/colorPrimary"
                android:gravity="center|start"
                android:padding="5dp"
                android:text="@string/delete"
                android:textColor="@android:color/white" />
        </LinearLayout>


    </LinearLayout>

</FrameLayout>