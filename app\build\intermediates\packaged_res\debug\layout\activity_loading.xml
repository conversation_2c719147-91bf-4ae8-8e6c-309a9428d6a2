<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#77000000">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="128dp"
        android:layout_gravity="bottom"
        android:background="#F8F8F8">

        <io.virtualapp.widgets.EatBeansView
            android:id="@+id/loading_anim"
            android:layout_width="match_parent"
            android:layout_height="30dp" />

        <ImageView
            android:id="@+id/app_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="64dp"
            android:layout_marginStart="64dp" />


        <TextView
            android:id="@+id/app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/app_icon"
            android:layout_marginLeft="20dp"
            android:layout_marginStart="20dp"
            android:layout_toEndOf="@id/app_icon"
            android:layout_toRightOf="@id/app_icon"
            android:gravity="center"
            android:paddingBottom="25dp"
            android:text="@string/preparing"
            android:textColor="@android:color/black" />

    </RelativeLayout>

</FrameLayout>