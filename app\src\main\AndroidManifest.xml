<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET"/>
    <application
        android:name=".VApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="TencentMapSDK"
            android:value="4HPBZ-2QWC6-H47SR-M6PZY-MTZB5-N2F4F"/>

        <activity
            android:name=".splash.SplashActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <activity
            android:name=".home.HomeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/UITheme"/>

        <activity
            android:name=".home.ListAppActivity"
            android:screenOrientation="portrait"
            android:theme="@style/UITheme"/>

        <activity
            android:name=".home.LoadingActivity"
            android:excludeFromRecents="true"
            android:noHistory="true"
            android:screenOrientation="portrait"
            android:taskAffinity="va.task.loading"
            android:theme="@style/TransparentTheme"/>
        <activity
            android:name=".home.location.VirtualLocationSettings"
            android:screenOrientation="portrait"
            android:theme="@style/UITheme"
            />

        <activity
            android:name=".home.location.MarkerActivity"
            android:screenOrientation="portrait"
            android:theme="@style/UITheme"
            />
    </application>


</manifest>
